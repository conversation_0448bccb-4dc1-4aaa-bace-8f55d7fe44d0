{
	"folders": [
		{
			"name": "🏠Root",
			"path": "."
		},
		// {
		// 	"name": "🚀MainCodeKt",
		// 	"path": "app/src/main/java/amobi/weather/forecast/storm/radar"
		// },
		// {
		// 	"name": "🧱AmobiCommon",
		// 	"path": "amobi_common/src/main/java/amobi/module/common"
		// },
	],
	"settings": {
		"explorer.autoReveal": true,
		"search.exclude": {},
		"workbench.editor.enablePreview": false,
		"workbench.editor.enablePreviewFromQuickOpen": false,
		"java.configuration.updateBuildConfiguration": "automatic",
		"favorites.sortDirection": "ASC",
	},
	"tasks": {
		"version": "2.0.0",
		"tasks": [
			{
				"label": "🚀Build 📦 Product Release⏳",
				"type": "shell",
				"command": "python3 build.py product release",
				"group": "build",
				"presentation": {
					"reveal": "silent",
					"panel": "new"
				},
				"options": {
					"cwd": "${workspaceFolder:🏠Root}"
				}
			},
			{
				"label": "🚀Build 📦 Product Debug🪲",
				"type": "shell",
				"command": "python3 build.py product debug",
				"group": "build",
				"presentation": {
					"reveal": "silent",
					"panel": "new"
				},
				"options": {
					"cwd": "${workspaceFolder:🏠Root}"
				}
			},
			{
				"label": "🚀Build 📦 Dev Release⏳",
				"type": "shell",
				"command": "python3 build.py dev release",
				"group": "build",
				"presentation": {
					"reveal": "silent",
					"panel": "new"
				},
				"options": {
					"cwd": "${workspaceFolder:🏠Root}"
				}
			},
			{
				"label": "🚀Build 📦 Dev Debug🪲",
				"type": "shell",
				"command": "python3 build.py dev debug",
				"group": "build",
				"presentation": {
					"reveal": "silent",
					"panel": "new"
				},
				"options": {
					"cwd": "${workspaceFolder:🏠Root}"
				}
			},
			{
				"label": "🚀Build 📦 Alpha Release⏳",
				"type": "shell",
				"command": "python3 build.py alpha release",
				"group": "build",
				"presentation": {
					"reveal": "silent",
					"panel": "new"
				},
				"options": {
					"cwd": "${workspaceFolder:🏠Root}"
				}
			},
			{
				"label": "🚀Build 📦 Alpha Debug🪲",
				"type": "shell",
				"command": "python3 build.py alpha debug",
				"group": "build",
				"presentation": {
					"reveal": "silent",
					"panel": "new"
				},
				"options": {
					"cwd": "${workspaceFolder:🏠Root}"
				}
			},
		]
	}
}