let currentMode = "default";

const modes = ["default", "fx", "abc"];
const modeElements = {
  default: document.querySelector(".default"),
  fx: document.querySelector(".fx"),
  abc: document.querySelector(".abc"),
};

const editorInstanceName = "editorInstance";
let guppy;
const actions = {
  clear: () => guppy.engine.set_content("<m><e/></m>"),

  exponential: () => {
    const content =
      guppy.engine.get_content() === "" ? "expPowTwo" : "exponential";
    guppy.engine.insert_symbol(content);
    guppy.engine.insert_string("2");
  },

  change: () => {
    // Chuyển đổi giữa các chế độ
    currentMode = modes[(modes.indexOf(currentMode) + 1) % modes.length];

    // Ẩn hoặc hiện tùy thuộc v<PERSON>o chế độ hiện tại
    modes.forEach((mode) => {
      modeElements[mode].classList.toggle("hidden", mode !== currentMode);
    });
  },

  leftarrow: () => guppy.engine.left(),

  rightarrow: () => guppy.engine.right(),

  enter: () => guppy.engine.insert_symbol("estwo"),

  backspace: () => guppy.engine.backspace(),

  sent: () => {
      const latexOutput = guppy.latex();
      if (latexOutput != " ") {
          console.log(latexOutput);
      }
  },

  default: (type) => guppy.engine.insert_string(type),
};

const input = (type) => {
  try {
    guppy.activate();
    // Kiểm tra nếu type là 'x', sửa thành '*'
    if (type === 'times') {
      type = '*';
    };
    if (type === 'divide') {
      type = '/';
    };
    console.log(type);
    (actions[type] || actions.default)(type);
    guppy.render(true);
  } catch (e) {
//    console.log(e.stack);
  }
};

window.onload = () => {
  guppy = new Guppy(editorInstanceName);
  guppy.event("change", function (e) {
    var a = document.getElementById("ans");
//    console.log(guppy.latex());
//    console.log(guppy.engine.get_content());

    try {
      var fn = e.target.func();
      a.innerHTML = fn({ pi: Math.PI, e: Math.E }).toFixed(3);
    } catch (e) {
      a.innerHTML = "Error";
    }
  });
};
