package com.amobilab.ezmath.ai.utils

import amobi.module.common.utils.dlog
import android.util.Log
import com.google.firebase.Firebase
import com.google.firebase.appcheck.appCheck
import kotlinx.coroutines.suspendCancellableCoroutine
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import java.io.IOException
import kotlin.coroutines.resume

object AppCheckUtils {
    interface ApiTestCallback {
        fun onSuccess(response: String?)
        fun onFailure(error: String?)
    }

    fun requestTestProtectedApi(callback: ApiTestCallback) {
        Firebase.appCheck.limitedUseAppCheckToken.addOnSuccessListener { appCheckTokenResult ->
//        FirebaseAppCheck.getInstance().getAppCheckToken(false).addOnSuccessListener { appCheckTokenResult ->
            val appCheckToken = appCheckTokenResult.token
            dlog("appCheckToken: $appCheckToken")

            val client = OkHttpClient()
            val url = "http://192.168.1.4:8787/v1/ez-math/auth/protected"
            val json = "{}" // Replace with your actual JSON body if needed
            val requestBody = json.toRequestBody("application/json".toMediaTypeOrNull())

            val request = Request.Builder()
                .url(url)
                .addHeader("x-portkey-provider", "openai")
                .addHeader("Content-Type", "application/json")
                .addHeader("X-Firebase-AppCheck", appCheckToken)
                .post(requestBody)
                .build()

            client.newCall(request).enqueue(object : okhttp3.Callback {
                override fun onFailure(call: okhttp3.Call, e: IOException) {
                    Log.e("ApiTestUtils", "Request failed: ${e.message}")
                    callback.onFailure(e.message)
                }

                override fun onResponse(call: okhttp3.Call, response: okhttp3.Response) {
                    val responseBody = response.body.string()
                    Log.d("ApiTestUtils", "Response: $responseBody")
                    callback.onSuccess(responseBody)
                }
            })
        }
            .addOnFailureListener { exception ->
                Log.e("ApiTestUtils", "Failed to get App Check token: ${exception.message}")
                callback.onFailure(exception.message)
            }
    }
    
    
    fun requestToken(callback: (String?) -> Unit) {
        Firebase.appCheck.limitedUseAppCheckToken.addOnSuccessListener { appCheckTokenResult ->
            val appCheckToken = appCheckTokenResult.token
            dlog("Success to get App Check token")
            callback(appCheckToken)
        }.addOnFailureListener { exception ->
            dlog("Failed to get App Check token: ${exception.message}")
            callback(null)
        }
    }

    suspend fun requestTokenSuspend(): String? = suspendCancellableCoroutine { cont ->
        Firebase.appCheck.limitedUseAppCheckToken
            .addOnSuccessListener { appCheckTokenResult ->
                val appCheckToken = appCheckTokenResult.token
                dlog("Success to get App Check token")
                cont.resume(appCheckToken)
            }
            .addOnFailureListener { exception ->
                dlog("Failed to get App Check token: ${exception.message}")
                cont.resume(null)
            }
    }
}