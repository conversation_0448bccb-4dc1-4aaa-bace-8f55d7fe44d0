package com.amobilab.ezmath.ai.di

import com.amobilab.ezmath.ai.data.network.services.GatewayApi
import com.amobilab.ezmath.ai.data.network.services.GeminiApi
import com.amobilab.ezmath.ai.data.network.services.GptApi
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object AppApiModule {
    @Provides
    @Singleton
    fun provideGeminiApi(): GeminiApi {
        return GeminiApi()
    }

    @Provides
    @Singleton
    fun provideGptApi(): GptApi {
        return GptApi()
    }

    @Provides
    @Singleton
    fun provideGatewayApi(): GatewayApi {
        return GatewayApi()
    }
}