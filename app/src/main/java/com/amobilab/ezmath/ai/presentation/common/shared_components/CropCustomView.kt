package com.amobilab.ezmath.ai.presentation.common.shared_components

import amobi.module.common.utils.MixedUtils
import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Bitmap
import android.graphics.BlurMaskFilter
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Path
import android.graphics.PorterDuff
import android.graphics.Rect
import android.graphics.RectF
import android.graphics.Region
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import androidx.core.content.ContextCompat
import androidx.core.graphics.toColorInt
import com.amobilab.ezmath.ai.R
import kotlin.math.abs
import kotlin.math.max
import kotlin.math.min

class CropCustomView : View {

    private val paint: Paint = Paint()
    private val paintFrame: Paint = Paint()
    private val paintText: Paint = Paint()
    private val frameRect: RectF = RectF(0F, 0F, 0F, 0F) // Default frame rectangle

    private var isSetLimitMove = false

    private var backgroundBitmap: Bitmap? = null

    private var showIcon = true
    private var drawableIconCameraRotate: Drawable? = null
    private var drawableIconFlash: Drawable? = null
    private var onIconCameraRotateClickListener: (() -> Unit)? = null
    private var onIconFlashClickListener: (() -> Unit)? = null

    private var frame = 1

    private var padding: Int = 150

    private var isSetDefaultFrameRect = false
    private var turnOnOffMove = false
    private var turnOnOffChangeSize = false
    private var turnOnOffAll = false
    private var turnOnOffIconFlash = false
    private var turnOnOffIconCamera = false

    // Lưu trữ vị trí chạm trước đó
    private var previousX: Float = 0f
    private var previousY: Float = 0f

    // Kích thước cạnh điều chỉnh khi ấn vào góc
    private val cornerAdjustSize = 100

    private var isTopRight = false
    private var isBottomLeft = false
    private var isTopLeft = false
    private var isBottomRight = false
    private var isMoving = false

    private val vien = 0F
    private var limitMoveLeft = 0
    private var limitMoveRight = width
    private var limitMoveTop = 300
    private var limitMoveBottom = 300 + width

    private val framePaint: Paint = Paint().apply {
        color = Color.BLACK
        style = Paint.Style.FILL
        strokeWidth = 5f
        alpha = 80
    }

    // Additional properties for scanning animation
    private var scanningX: Float = -10f
    private val scanPaint: Paint = Paint().apply {
        color = Color.WHITE
        strokeWidth = 5f
        style = Paint.Style.FILL_AND_STROKE
        maskFilter = BlurMaskFilter(20f, BlurMaskFilter.Blur.SOLID)
        isAntiAlias = true
    }
    private val scanDuration: Long = 500 // Adjust the duration as needed

    constructor(context: Context) : super(context) {
        init()
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init()
    }

    constructor(context: Context, attrs: AttributeSet?, defStyle: Int) : super(context, attrs, defStyle) {
        init()
    }


    private var drawableBottomLeft: Drawable? = null
    private var drawableBottomRight: Drawable? = null
    private var drawableTopLeft: Drawable? = null
    private var drawableTopRight: Drawable? = null
    private var drawableCenterHorizontal: Drawable? = null
    private var drawableCenterVertical: Drawable? = null

    private var drawableBottomLeftV2: Drawable? = null
    private var drawableBottomRightV2: Drawable? = null
    private var drawableTopLeftV2: Drawable? = null
    private var drawableTopRightV2: Drawable? = null
    private var drawableCenterHorizontalV2: Drawable? = null
    private var drawableCenterVerticalV2: Drawable? = null

    private var drawableBottomLeftV4: Drawable? = null
    private var drawableBottomRightV4: Drawable? = null
    private var drawableTopLeftV4: Drawable? = null
    private var drawableTopRightV4: Drawable? = null


    private fun init() {
        drawableIconCameraRotate = ContextCompat.getDrawable(context, R.drawable.ic_camera_rotate)
        drawableIconFlash = ContextCompat.getDrawable(context, R.drawable.ic_flash)

        paint.color = ContextCompat.getColor(context, R.color.colorPrimary)
        paint.style = Paint.Style.STROKE
        paint.strokeWidth = 5f

        paintFrame.color = "#E6C083".toColorInt()
        paintFrame.style = Paint.Style.STROKE
        paintFrame.strokeWidth = 2f

        // Cấu hình thuộc tính về văn bản
        paintText.color = Color.BLUE
        paintText.textSize = 40f
        paintText.textAlign = Paint.Align.CENTER


        drawableBottomLeft = ContextCompat.getDrawable(context, R.drawable.svg_ic_scan_bottom_left)
        drawableBottomRight = ContextCompat.getDrawable(context, R.drawable.svg_ic_scan_bottom_right)
        drawableTopLeft = ContextCompat.getDrawable(context, R.drawable.svg_ic_scan_top_left)
        drawableTopRight = ContextCompat.getDrawable(context, R.drawable.svg_ic_scan_top_right)
        drawableCenterVertical = ContextCompat.getDrawable(context, R.drawable.svg_ic_scan_center_vertical)
        drawableCenterHorizontal = ContextCompat.getDrawable(context, R.drawable.svg_ic_scan_center_horizontal)

        drawableBottomLeftV2 = ContextCompat.getDrawable(context, R.drawable.svg_ic_scan_bottom_left_v2)
        drawableBottomRightV2 = ContextCompat.getDrawable(context, R.drawable.svg_ic_scan_bottom_right_v2)
        drawableTopLeftV2 = ContextCompat.getDrawable(context, R.drawable.svg_ic_scan_top_left_v2)
        drawableTopRightV2 = ContextCompat.getDrawable(context, R.drawable.svg_ic_scan_top_right_v2)
        drawableCenterVerticalV2 = ContextCompat.getDrawable(context, R.drawable.svg_ic_scan_center_vertical_v2)
        drawableCenterHorizontalV2 = ContextCompat.getDrawable(context, R.drawable.svg_ic_scan_center_horizontal_v2)

        drawableBottomLeftV4 = ContextCompat.getDrawable(context, R.drawable.svg_ic_scan_bottom_left_v4)
        drawableBottomRightV4 = ContextCompat.getDrawable(context, R.drawable.svg_ic_scan_bottom_right_v4)
        drawableTopLeftV4 = ContextCompat.getDrawable(context, R.drawable.svg_ic_scan_top_left_v4)
        drawableTopRightV4 = ContextCompat.getDrawable(context, R.drawable.svg_ic_scan_top_right_v4)
    }


    private val minFrameSize = 400f

    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(event: MotionEvent): Boolean {
        val edgeTouchThreshold = MixedUtils.dp2px(30f)
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                if (showIcon) {
                    val iconSize = MixedUtils.dp2px(24f)
                    val iconLeft = frameRect.right - iconSize
                    val iconTop = frameRect.top - iconSize - iconSize / 4
                    val iconLeft2 = iconLeft - iconSize

                    if (event.x in iconLeft..frameRect.right && event.y in iconTop..(iconTop + iconSize)) {
                        onIconCameraRotateClickListener?.invoke()
                    }
                    if (event.x in iconLeft2 - iconSize / 2..iconLeft - iconSize / 2 && event.y in iconTop..(iconTop + iconSize)) {
                        onIconFlashClickListener?.invoke()
                    }
                }

                previousX = event.x
                previousY = event.y

                isTopRight = (abs(previousX - frameRect.right) <= edgeTouchThreshold &&
                        abs(previousY - frameRect.top) <= edgeTouchThreshold)
                isBottomLeft = (abs(previousX - frameRect.left) <= edgeTouchThreshold &&
                        abs(previousY - frameRect.bottom) <= edgeTouchThreshold)
                isTopLeft = (abs(previousX - frameRect.left) <= edgeTouchThreshold &&
                        abs(previousY - frameRect.top) <= edgeTouchThreshold)
                isBottomRight = (abs(previousX - frameRect.right) <= edgeTouchThreshold &&
                        abs(previousY - frameRect.bottom) <= edgeTouchThreshold)

                isMoving = !isTopRight && !isBottomLeft && !isTopLeft && !isBottomRight &&
                        frameRect.contains(previousX.toInt().toFloat(), previousY.toInt().toFloat())

                return true
            }

            MotionEvent.ACTION_MOVE -> {
                if (!turnOnOffAll) return true

                val deltaX = event.x - previousX
                val deltaY = event.y - previousY

                when {
                    isTopRight && turnOnOffChangeSize -> {
                        frameRect.right = max(frameRect.left + minFrameSize, frameRect.right + deltaX.toInt())
                        frameRect.top = min(frameRect.bottom - minFrameSize, frameRect.top + deltaY.toInt())
                    }

                    isBottomLeft && turnOnOffChangeSize -> {
                        frameRect.left = min(frameRect.right - minFrameSize, frameRect.left + deltaX.toInt())
                        frameRect.bottom = max(frameRect.top + minFrameSize, frameRect.bottom + deltaY.toInt())
                    }

                    isTopLeft && turnOnOffChangeSize -> {
                        frameRect.left = min(frameRect.right - minFrameSize, frameRect.left + deltaX.toInt())
                        frameRect.top = min(frameRect.bottom - minFrameSize, frameRect.top + deltaY.toInt())
                    }

                    isBottomRight && turnOnOffChangeSize -> {
                        frameRect.right = max(frameRect.left + minFrameSize, frameRect.right + deltaX.toInt())
                        frameRect.bottom = max(frameRect.top + minFrameSize, frameRect.bottom + deltaY.toInt())
                    }

                    // Resize cạnh trên
                    abs(previousY - frameRect.top) <= edgeTouchThreshold &&
                            previousX in frameRect.left..frameRect.right &&
                            turnOnOffChangeSize -> {
                        frameRect.top = min(frameRect.bottom - minFrameSize, frameRect.top + deltaY.toInt())
                    }

                    // Resize cạnh dưới
                    abs(previousY - frameRect.bottom) <= edgeTouchThreshold &&
                            previousX in frameRect.left..frameRect.right &&
                            turnOnOffChangeSize -> {
                        frameRect.bottom = max(frameRect.top + minFrameSize, frameRect.bottom + deltaY.toInt())
                    }

                    // Resize cạnh trái
                    abs(previousX - frameRect.left) <= edgeTouchThreshold &&
                            previousY in frameRect.top..frameRect.bottom &&
                            turnOnOffChangeSize -> {
                        frameRect.left = min(frameRect.right - minFrameSize, frameRect.left + deltaX.toInt())
                    }

                    // Resize cạnh phải
                    abs(previousX - frameRect.right) <= edgeTouchThreshold &&
                            previousY in frameRect.top..frameRect.bottom &&
                            turnOnOffChangeSize -> {
                        frameRect.right = max(frameRect.left + minFrameSize, frameRect.right + deltaX.toInt())
                    }

                    // Di chuyển toàn khung
                    isMoving && turnOnOffMove -> {
                        frameRect.left += deltaX.toInt()
                        frameRect.top += deltaY.toInt()
                        frameRect.right += deltaX.toInt()
                        frameRect.bottom += deltaY.toInt()
                    }
                }

                if (isSetLimitMove) {
                    frameRect.left = max(limitMoveLeft.toFloat(), frameRect.left)
                    frameRect.top = max(limitMoveTop + vien, frameRect.top)
                    frameRect.right = min(limitMoveRight.toFloat(), frameRect.right)
                    frameRect.bottom = min(limitMoveBottom - vien, frameRect.bottom)
                }

                previousX = event.x
                previousY = event.y
                postInvalidateOnAnimation()
                return true
            }

            MotionEvent.ACTION_UP -> {
                isTopRight = false
                isBottomLeft = false
                isTopLeft = false
                isBottomRight = false
                isMoving = false
            }
        }
        return super.onTouchEvent(event)
    }


    fun setBackgroundBitmap(bitmap: Bitmap) {
        backgroundBitmap = bitmap
        invalidate() // Request a redraw to update the background
    }


    fun turnOnOffMove(boolean: Boolean) {
        turnOnOffMove = boolean
    }

    fun turnOnOffChangeSize(boolean: Boolean) {
        turnOnOffChangeSize = boolean
    }

    fun turnOnOffAll(boolean: Boolean) {
        turnOnOffAll = boolean
    }
    fun setModeFrame(mode: Int) {
        frame = mode
    }

    fun setFrameRect(rectF: RectF) {
        isSetDefaultFrameRect = true
        frameRect.set(
            rectF.left,
            rectF.top,
            rectF.right,
            rectF.bottom
        )
        invalidate()
    }

    fun showIcon(isShowIcon: Boolean) {
        showIcon = isShowIcon
    }

    fun setLimitMove(limitMoveLeft: Int, limitMoveRight: Int, limitMoveTop: Int, limitMoveBottom: Int) {
        isSetLimitMove = true
        this.limitMoveLeft = limitMoveLeft
        this.limitMoveRight = limitMoveRight
        this.limitMoveTop = limitMoveTop
        this.limitMoveBottom = limitMoveBottom
        invalidate()
    }

    // Phương thức để set listener từ bên ngoài
    fun setOnIconCameraRotateClickListener(listener: (() -> Unit)?) {
        onIconCameraRotateClickListener = listener
    }

    // Phương thức để set listener từ bên ngoài
    fun setOnIconFlashClickListener(listener: (() -> Unit)?) {
        onIconFlashClickListener = listener
    }

    fun setTurnOnOffIconFlash(boolean: Boolean) {
        turnOnOffIconFlash = boolean
        invalidate()
    }

    fun setTurnOnOffIconCamera(boolean: Boolean) {
        turnOnOffIconCamera = boolean
        invalidate()
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        // Draw the background bitmap if it's not null
        backgroundBitmap?.let { bitmap ->
            // Get the bitmap's dimensions
            val bitmapWidth = bitmap.width
            val bitmapHeight = bitmap.height

            // Calculate the scale factors to fit the bitmap inside the view
            val scaleWidth = width.toFloat() / bitmapWidth
            val scaleHeight = height.toFloat() / bitmapHeight
            val scale = minOf(scaleWidth, scaleHeight)

            // Calculate the new dimensions of the bitmap
            val scaledWidth = bitmapWidth * scale
            val scaledHeight = bitmapHeight * scale

            // Calculate the offset to center the bitmap
            val offsetX = (width - scaledWidth) / 2
            val offsetY = (height - scaledHeight) / 2

            // Draw the bitmap with the new dimensions and position
            val rectF = RectF(offsetX, offsetY, offsetX + scaledWidth, offsetY + scaledHeight)
            canvas.drawBitmap(bitmap, null, rectF, null)
        }

        val cornerRadius = if (frame == 3 || frame == 4) {
            0f
        } else {
            50f
        }
        // Draw the scanning line
        canvas.drawLine(scanningX, frameRect.top + cornerRadius, scanningX, frameRect.bottom - cornerRadius, scanPaint)

        // Tạo một path với hình dạng của frameRect
        val path = Path().apply {
            addRoundRect(RectF(frameRect), cornerRadius, cornerRadius, Path.Direction.CW)
        }

        // Lưu trữ một bản sao của canvas
        val saveCount = canvas.save()

        // Cắt bớt phần nền đen để chỉ bao gồm nền đen xung quanh frameRect
        canvas.clipPath(path, Region.Op.DIFFERENCE)

        // Vẽ nền đen xung quanh frameRect
        val blackRect = RectF(0f, 0f, width.toFloat(), height.toFloat())
        canvas.drawRect(blackRect, framePaint)

        // Khôi phục canvas sau khi vẽ nền đen
        canvas.restoreToCount(saveCount)

        when (frame){
            1 -> {
                // Draw the frame rectangle with rounded corners
                canvas.drawRoundRect(RectF(frameRect), cornerRadius, cornerRadius, paint)
            }
            2 -> {
                // Draw the corner drawables inside the frameRect
                drawableTopLeft?.let { drawable ->
                    drawable.setBounds(
                        frameRect.left.toInt(),
                        frameRect.top.toInt(),
                        frameRect.left.toInt() + drawable.intrinsicWidth,
                        frameRect.top.toInt() + drawable.intrinsicHeight
                    )
                    drawable.draw(canvas)
                }

                drawableTopRight?.let { drawable ->
                    drawable.setBounds(
                        frameRect.right.toInt() - drawable.intrinsicWidth,
                        frameRect.top.toInt(),
                        frameRect.right.toInt(),
                        frameRect.top.toInt() + drawable.intrinsicHeight
                    )
                    drawable.draw(canvas)
                }

                drawableBottomLeft?.let { drawable ->
                    drawable.setBounds(
                        frameRect.left.toInt(),
                        frameRect.bottom.toInt() - drawable.intrinsicHeight,
                        frameRect.left.toInt() + drawable.intrinsicWidth,
                        frameRect.bottom.toInt()
                    )
                    drawable.draw(canvas)
                }

                drawableBottomRight?.let { drawable ->
                    drawable.setBounds(
                        frameRect.right.toInt() - drawable.intrinsicWidth,
                        frameRect.bottom.toInt() - drawable.intrinsicHeight,
                        frameRect.right.toInt(),
                        frameRect.bottom.toInt()
                    )
                    drawable.draw(canvas)
                }

                // Draw the center drawables on each side of the frameRect
                drawableCenterVertical?.let { drawable ->
                    // Center on left side
                    val centerYLeft = (frameRect.top + frameRect.bottom) / 2
                    drawable.setBounds(
                        frameRect.left.toInt(),
                        (centerYLeft - drawable.intrinsicHeight / 2).toInt(),
                        (frameRect.left + drawable.intrinsicWidth).toInt(),
                        (centerYLeft + drawable.intrinsicHeight / 2).toInt()
                    )
                    drawable.draw(canvas)

                    // Center on right side
                    val centerYRight = (frameRect.top + frameRect.bottom) / 2
                    drawable.setBounds(
                        (frameRect.right - drawable.intrinsicWidth).toInt(),
                        (centerYRight - drawable.intrinsicHeight / 2).toInt(),
                        frameRect.right.toInt(),
                        (centerYRight + drawable.intrinsicHeight / 2).toInt()
                    )
                    drawable.draw(canvas)
                }

                // Draw the center drawables on each side of the frameRect
                drawableCenterHorizontal?.let { drawable ->
                    // Center on top side
                    val centerXTop = (frameRect.left + frameRect.right) / 2
                    drawable.setBounds(
                        (centerXTop - drawable.intrinsicWidth / 2).toInt(),
                        frameRect.top.toInt(),
                        (centerXTop + drawable.intrinsicWidth / 2).toInt(),
                        (frameRect.top + drawable.intrinsicHeight).toInt()
                    )
                    drawable.draw(canvas)

                    // Center on bottom side
                    val centerXBottom = (frameRect.left + frameRect.right) / 2
                    drawable.setBounds(
                        (centerXBottom - drawable.intrinsicWidth / 2).toInt(),
                        (frameRect.bottom - drawable.intrinsicHeight).toInt(),
                        (centerXBottom + drawable.intrinsicWidth / 2).toInt(),
                        frameRect.bottom.toInt()
                    )
                    drawable.draw(canvas)
                }
            }
            3 -> {
                val paddingDrawable = 12

                canvas.drawRoundRect(RectF(frameRect), cornerRadius, cornerRadius, paintFrame)
                // Draw the corner drawables inside the frameRect
                drawableTopLeftV2?.let { drawable ->
                    drawable.setBounds(
                        frameRect.left.toInt() - paddingDrawable,
                        frameRect.top.toInt() - paddingDrawable,
                        frameRect.left.toInt() + drawable.intrinsicWidth - paddingDrawable,
                        frameRect.top.toInt() + drawable.intrinsicHeight - paddingDrawable
                    )
                    drawable.draw(canvas)
                }

                drawableTopRightV2?.let { drawable ->
                    drawable.setBounds(
                        frameRect.right.toInt() - drawable.intrinsicWidth + paddingDrawable,
                        frameRect.top.toInt() - paddingDrawable,
                        frameRect.right.toInt() + paddingDrawable,
                        frameRect.top.toInt() + drawable.intrinsicHeight - paddingDrawable
                    )
                    drawable.draw(canvas)
                }

                drawableBottomLeftV2?.let { drawable ->
                    drawable.setBounds(
                        frameRect.left.toInt() - paddingDrawable,
                        frameRect.bottom.toInt() - drawable.intrinsicHeight + paddingDrawable,
                        frameRect.left.toInt() + drawable.intrinsicWidth - paddingDrawable,
                        frameRect.bottom.toInt() + paddingDrawable
                    )
                    drawable.draw(canvas)
                }

                drawableBottomRightV2?.let { drawable ->
                    drawable.setBounds(
                        frameRect.right.toInt() - drawable.intrinsicWidth + paddingDrawable,
                        frameRect.bottom.toInt() - drawable.intrinsicHeight + paddingDrawable,
                        frameRect.right.toInt() + paddingDrawable,
                        frameRect.bottom.toInt() + paddingDrawable
                    )
                    drawable.draw(canvas)
                }

                // Draw the center drawables on each side of the frameRect
                drawableCenterVerticalV2?.let { drawable ->
                    // Center on left side
                    val centerYLeft = (frameRect.top + frameRect.bottom) / 2
                    drawable.setBounds(
                        frameRect.left.toInt() - paddingDrawable,
                        (centerYLeft - drawable.intrinsicHeight / 2).toInt() - paddingDrawable,
                        (frameRect.left + drawable.intrinsicWidth).toInt() - paddingDrawable,
                        (centerYLeft + drawable.intrinsicHeight / 2).toInt() - paddingDrawable
                    )
                    drawable.draw(canvas)

                    // Center on right side
                    val centerYRight = (frameRect.top + frameRect.bottom) / 2
                    drawable.setBounds(
                        (frameRect.right - drawable.intrinsicWidth).toInt() + paddingDrawable,
                        (centerYRight - drawable.intrinsicHeight / 2).toInt() + paddingDrawable,
                        frameRect.right.toInt() + paddingDrawable,
                        (centerYRight + drawable.intrinsicHeight / 2).toInt() + paddingDrawable
                    )
                    drawable.draw(canvas)
                }

                // Draw the center drawables on each side of the frameRect
                drawableCenterHorizontalV2?.let { drawable ->
                    // Center on top side
                    val centerXTop = (frameRect.left + frameRect.right) / 2
                    drawable.setBounds(
                        (centerXTop - drawable.intrinsicWidth / 2).toInt() - paddingDrawable,
                        frameRect.top.toInt() - paddingDrawable,
                        (centerXTop + drawable.intrinsicWidth / 2).toInt() - paddingDrawable,
                        (frameRect.top + drawable.intrinsicHeight).toInt() - paddingDrawable
                    )
                    drawable.draw(canvas)

                    // Center on bottom side
                    val centerXBottom = (frameRect.left + frameRect.right) / 2
                    drawable.setBounds(
                        (centerXBottom - drawable.intrinsicWidth / 2).toInt() + paddingDrawable,
                        (frameRect.bottom - drawable.intrinsicHeight).toInt() + paddingDrawable,
                        (centerXBottom + drawable.intrinsicWidth / 2).toInt() + paddingDrawable,
                        frameRect.bottom.toInt() + paddingDrawable
                    )
                    drawable.draw(canvas)
                }
            }
            4 -> {
                // Draw the corner drawables inside the frameRect
                drawableTopLeftV4?.let { drawable ->
                    drawable.setBounds(
                        frameRect.left.toInt(),
                        frameRect.top.toInt(),
                        frameRect.left.toInt() + drawable.intrinsicWidth,
                        frameRect.top.toInt() + drawable.intrinsicHeight
                    )
                    drawable.draw(canvas)
                }

                drawableTopRightV4?.let { drawable ->
                    drawable.setBounds(
                        frameRect.right.toInt() - drawable.intrinsicWidth,
                        frameRect.top.toInt(),
                        frameRect.right.toInt(),
                        frameRect.top.toInt() + drawable.intrinsicHeight
                    )
                    drawable.draw(canvas)
                }

                drawableBottomLeftV4?.let { drawable ->
                    drawable.setBounds(
                        frameRect.left.toInt(),
                        frameRect.bottom.toInt() - drawable.intrinsicHeight,
                        frameRect.left.toInt() + drawable.intrinsicWidth,
                        frameRect.bottom.toInt()
                    )
                    drawable.draw(canvas)
                }

                drawableBottomRightV4?.let { drawable ->
                    drawable.setBounds(
                        frameRect.right.toInt() - drawable.intrinsicWidth,
                        frameRect.bottom.toInt() - drawable.intrinsicHeight,
                        frameRect.right.toInt(),
                        frameRect.bottom.toInt()
                    )
                    drawable.draw(canvas)
                }
            }
            else -> {
                // Draw the frame rectangle with rounded corners
                canvas.drawRoundRect(RectF(frameRect), cornerRadius, cornerRadius, paint)
            }
        }

        if (showIcon) {
            val iconSize = MixedUtils.dp2px(24f)
            val iconLeft = frameRect.right - iconSize
            val iconTop = frameRect.top - iconSize - iconSize / 4
            val iconLeft2 = iconLeft - iconSize

            // Vẽ icon ở góc trên cùng bên phải của frameRect
            drawableIconCameraRotate?.let { icon ->
                icon.setBounds(iconLeft.toInt(), iconTop.toInt(), frameRect.right.toInt(), (iconTop + iconSize).toInt())
                icon.draw(canvas)
                icon.clearColorFilter()
            }
            // Vẽ icon2 ở góc trên cùng bên phải của frameRect
            drawableIconFlash?.let { icon ->
                icon.setBounds(iconLeft2.toInt() - iconSize / 2, iconTop.toInt(), iconLeft.toInt() - iconSize / 2, (iconTop + iconSize).toInt())
                if (turnOnOffIconCamera) {
                    icon.setColorFilter(Color.GRAY, PorterDuff.Mode.SRC_IN)
                } else if (turnOnOffIconFlash) {
                    icon.setColorFilter(Color.BLUE, PorterDuff.Mode.SRC_IN)
                }
                icon.draw(canvas)
                icon.clearColorFilter()
            }
        }
    }

    fun rotateBackgroundBitmap(degrees: Float) {
        backgroundBitmap?.let { original ->
            val matrix = android.graphics.Matrix().apply {
                postRotate(degrees)
            }

            // Tạo bitmap đã xoay
            val rotatedBitmap = Bitmap.createBitmap(
                original,
                0, 0,
                original.width,
                original.height,
                matrix,
                true
            )

            // Cập nhật lại background
            backgroundBitmap = rotatedBitmap
            invalidate()
        }
    }



    fun getRectOnScreen(): RectF {
        val left = frameRect.left
        val top = frameRect.top
        val right = frameRect.right
        val bottom = frameRect.bottom

        return RectF(left, top, right, bottom)
    }

    fun captureCanvas(): Bitmap {
        // Create a bitmap with the same dimensions as the view
        val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bitmap)

        // Draw the current content of the view on the bitmap
        draw(canvas)

        return bitmap
    }

    fun getCroppedBitmapFromCapture(): Bitmap? {
        if (width == 0 || height == 0) {
            return null
        }

        // Capture the entire canvas as a bitmap
        val capturedBitmap = captureCanvas()

        // Get the dimensions of the captured bitmap
        val bitmapWidth = capturedBitmap.width
        val bitmapHeight = capturedBitmap.height

        // Get the rect on screen that defines the crop area
        val rectOnScreen = getRectOnScreen()

        // Convert rectOnScreen from view coordinates to bitmap coordinates
        val scale = minOf(width.toFloat() / bitmapWidth, height.toFloat() / bitmapHeight)

        val dpToPx = 4 * context.resources.displayMetrics.density

        // Adjust the crop area to reduce by 4 px or dp
        val bitmapRectF = RectF(
            (rectOnScreen.left / scale) + dpToPx,
            (rectOnScreen.top / scale) + dpToPx,
            (rectOnScreen.right / scale) - dpToPx,
            (rectOnScreen.bottom / scale) - dpToPx
        )


        // Ensure the coordinates are within bitmap bounds
        val cropLeft = max(0f, bitmapRectF.left)
        val cropTop = max(0f, bitmapRectF.top)
        val cropRight = min(bitmapWidth.toFloat(), bitmapRectF.right)
        val cropBottom = min(bitmapHeight.toFloat(), bitmapRectF.bottom)

        // Calculate the width and height of the cropped bitmap
        val cropWidth = (cropRight - cropLeft).toInt()
        val cropHeight = (cropBottom - cropTop).toInt()

        // If the crop width or height is non-positive, return null
        if (cropWidth <= 0 || cropHeight <= 0) {
            return null
        }

        // Create a new bitmap to store the cropped image
        val croppedBitmap = Bitmap.createBitmap(cropWidth, cropHeight, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(croppedBitmap)
        val srcRect = Rect(cropLeft.toInt(), cropTop.toInt(), cropRight.toInt(), cropBottom.toInt())
        val dstRect = Rect(0, 0, cropWidth, cropHeight)

        // Draw the cropped portion onto the new bitmap
        canvas.drawBitmap(capturedBitmap, srcRect, dstRect, null)

        return croppedBitmap
    }

}