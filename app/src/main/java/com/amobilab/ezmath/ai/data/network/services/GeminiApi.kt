package com.amobilab.ezmath.ai.data.network.services

import amobi.module.common.CommApplication
import amobi.module.common.configs.PrefAssist
import amobi.module.common.configs.RconfAssist
import amobi.module.common.utils.MixedUtils
import amobi.module.common.utils.debugLog
import android.content.Context
import android.graphics.Bitmap
import com.amobilab.ezmath.ai.R
import com.amobilab.ezmath.ai.data.models.Chat
import com.amobilab.ezmath.ai.data.network.models.ChatResponse
import com.amobilab.ezmath.ai.data.pref.PrefConst
import com.amobilab.ezmath.ai.data.pref.RconfConst
import com.amobilab.ezmath.ai.presentation.common.shared_values.BotType
import com.amobilab.ezmath.ai.presentation.common.shared_values.ChatQuestionMode
import com.amobilab.ezmath.ai.values.Const
import com.google.firebase.Firebase
import com.google.firebase.vertexai.type.content
import com.google.firebase.vertexai.type.generationConfig
import com.google.firebase.vertexai.vertexAI
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.takeWhile
import kotlinx.coroutines.withContext

class GeminiApi {

    companion object {
        val systemPromptRenderMath = ".\nWhen presenting math formulas, strictly follow rules katex.\n"
    }
    val isCollecting = MutableStateFlow(true)

    fun stopCollecting() {
        isCollecting.value = false
    }

    suspend fun getResponse(prompt: String, mode: ChatQuestionMode): ChatResponse {
        debugLog("AI Get Response: $mode")
        val systemInstruction = when (mode) {
            ChatQuestionMode.Translate ->
                CommApplication.appContext.getString(mode.instructionId, PrefAssist.getString(PrefConst.TRANSLATE_TARGET))

            else -> CommApplication.appContext.getString(mode.instructionId)
        }
        val generativeModel = Firebase.vertexAI.generativeModel(
            modelName = Const.AiModelName.GEMINI,
//            apiKey = BuildConfig.GOOGLE_API_KEY,
            // generationConfig = generationConfig { maxOutputTokens = 0 },
            systemInstruction = content { text(systemInstruction + systemPromptRenderMath) },
        )

        return try {
            val response = withContext(Dispatchers.IO) {
                generativeModel.generateContent(prompt)
            }
            val result = response.text
            ChatResponse(
                chat = Chat(
                    prompt = result ?: CommApplication.appContext.getString(R.string.errors_please_try_again),
                    bitmap = null,
                    isFromUser = false,
                    isError = false,
                    botType = BotType.BOT_GEMINI
                ),
                inputTokenCount = response.usageMetadata?.promptTokenCount ?: 0,
                outputTokenCount = response.usageMetadata?.candidatesTokenCount ?: 0,
                totalTokenCount = response.usageMetadata?.totalTokenCount ?: 0
            )
        } catch (e: Exception) {
            ChatResponse(
                chat = Chat(
                    prompt = e.message ?: CommApplication.appContext.getString(R.string.errors_please_try_again),
                    bitmap = null,
                    isFromUser = false,
                    isError = true,
                    botType = BotType.BOT_GEMINI
                ),
                inputTokenCount = 0,
                outputTokenCount = 0,
                totalTokenCount = 0
            )
        }
    }

    fun getResponses(context: Context, listChat: MutableList<Chat>, prompts: String, mode: ChatQuestionMode): Flow<ChatResponse?> = flow {
        debugLog("AI Get Response: $mode")
        val systemInstruction = when (mode) {
            ChatQuestionMode.Translate ->
                CommApplication.appContext.getString(mode.instructionId, PrefAssist.getString(PrefConst.TRANSLATE_TARGET))

            else -> CommApplication.appContext.getString(mode.instructionId)
        }

        val generativeModel = Firebase.vertexAI.generativeModel(
            modelName = Const.AiModelName.GEMINI,
//            apiKey = BuildConfig.GOOGLE_API_KEY,
            // generationConfig = generationConfig { maxOutputTokens = 0 },
            systemInstruction = content { text(systemInstruction + systemPromptRenderMath) },
            generationConfig = generationConfig {
                temperature = if (RconfAssist.getBoolean(RconfConst.IS_NOT_RANDOM_RESPONSE)) 0f else 1f
                topP = if (RconfAssist.getBoolean(RconfConst.IS_NOT_RANDOM_RESPONSE)) 0f else 1f
                maxOutputTokens = 1024
            }
        )
        isCollecting.value = true

        var isSentError = true
        try {
            val response = withContext(Dispatchers.IO) {
                val chat = generativeModel.startChat(
                    history = listChat.take(6).reversed().map { item ->
                        if (item.isFromUser) {
                            content(role = "user") {
                                if (item.bitmap != null) {
                                    image(item.bitmap)
                                }
                                text(item.prompt)
                            }
                        } else {
                            content(role = "model") {
                                text(item.prompt)
                            }
                        }
                    }
                )
                chat.sendMessageStream(prompts)
            }

            response
                .takeWhile { isCollecting.value }
                .onCompletion {
                    debugLog("onCompletion : xong")
                }
                .collect { it ->
                    isSentError = false
                    val promptTokenCount = it.usageMetadata?.promptTokenCount ?: 0
                    val candidatesTokenCount = it.usageMetadata?.candidatesTokenCount ?: 0
                    val totalTokenCount = it.usageMetadata?.totalTokenCount ?: 0

                    debugLog("response23 gemini promptTokenCount ${promptTokenCount} candidatesTokenCount ${candidatesTokenCount} totalTokenCount ${totalTokenCount} , response ${response.toString()}")

                    emit(
                        ChatResponse(
                            chat = Chat(
                                prompt = it.text ?: context.getString(R.string.errors_please_try_again),
                                bitmap = null,
                                isFromUser = false,
                                isError = false,
                                botType = BotType.BOT_GEMINI
                            ),
                            inputTokenCount = promptTokenCount,
                            outputTokenCount = candidatesTokenCount,
                            totalTokenCount = totalTokenCount
                        )
                    )
                }
        } catch (e: Exception) {
            debugLog("Error: ${e.message}")
            if (isSentError) {
                emit(
                    ChatResponse(
                        chat = Chat(
                            prompt = context.getString(R.string.errors_please_try_again),
                            bitmap = null,
                            isFromUser = false,
                            isError = true,
                            botType = BotType.BOT_GEMINI
                        ),
                        inputTokenCount = 0,
                        outputTokenCount = 0,
                        totalTokenCount = 0
                    )
                )
            }
            withContext(Dispatchers.Main) {
                MixedUtils.showToast(context, R.string.errors_please_try_again)
            }
        }
    }

    fun getResponseWithImage(context: Context, prompt: String, bitmap: Bitmap, mode: ChatQuestionMode): Flow<ChatResponse?> = flow {
        debugLog("AI Get Response: $mode")

        val systemInstruction = when (mode) {
            ChatQuestionMode.Translate ->
                CommApplication.appContext.getString(mode.instructionId, PrefAssist.getString(PrefConst.TRANSLATE_TARGET))

            else -> CommApplication.appContext.getString(mode.instructionId)
        }
        val promptImage = when (mode) {
            ChatQuestionMode.Translate ->
                context.getString(mode.promptImageId, PrefAssist.getString(PrefConst.TRANSLATE_TARGET))

            else -> context.getString(mode.promptImageId)
        }

        val generativeModel = Firebase.vertexAI.generativeModel(
            modelName = Const.AiModelName.GEMINI,
//            apiKey = BuildConfig.GOOGLE_API_KEY,
            // generationConfig = generationConfig { maxOutputTokens = 0 },
            systemInstruction = content { text(systemInstruction + systemPromptRenderMath) },
            generationConfig = generationConfig {
                temperature = if (RconfAssist.getBoolean(RconfConst.IS_NOT_RANDOM_RESPONSE)) 0f else 1f
                topP = if (RconfAssist.getBoolean(RconfConst.IS_NOT_RANDOM_RESPONSE)) 0f else 1f
            }
        )

        val inputContent = content {
            image(bitmap)
            text("$promptImage: $prompt")
        }

        var isSentError = true
        // Sử dụng Flow.catch để xử lý lỗi
        try {
            val responseFlow = generativeModel.generateContentStream(inputContent)

            // Thu thập và phát giá trị từ responseFlow
            responseFlow.collect { result ->
                debugLog("response ${result.text}")
                val usageMetadata = result.usageMetadata
                isSentError = false
                emit(
                    ChatResponse(
                        chat = Chat(
                            prompt = result.text ?: CommApplication.appContext.getString(R.string.errors_please_try_again),
                            bitmap = null,
                            isFromUser = false,
                            isError = false,
                            botType = BotType.BOT_GEMINI
                        ),
                        inputTokenCount = usageMetadata?.promptTokenCount ?: 0,
                        outputTokenCount = usageMetadata?.candidatesTokenCount ?: 0,
                        totalTokenCount = usageMetadata?.totalTokenCount ?: 0
                    )
                )
            }
        } catch (e: Exception) {
            debugLog("Error: ${e.message}")
            if (isSentError) {
                emit(
                    ChatResponse(
                        chat = Chat(
                            prompt = context.getString(R.string.errors_please_try_again),
                            bitmap = null,
                            isFromUser = false,
                            isError = true,
                            botType = BotType.BOT_GEMINI
                        ),
                        inputTokenCount = 0,
                        outputTokenCount = 0,
                        totalTokenCount = 0
                    )
                )
            }
            withContext(Dispatchers.Main) {
                MixedUtils.showToast(context, R.string.errors_please_try_again)
            }
        }
    }
//        .catch { e ->
//        emit(ChatResponse(
//            chat = Chat(prompt = "Flow error: ${e.message}", bitmap = null, isFromUser = false, botType = BotType.BOT_GEMINI),
//            promptTokenCount = 0,
//            candidatesTokenCount = 0,
//            totalTokenCount = 0
//        ))
//    }

    suspend fun getResponseWithImageNoStream(prompt: String, bitmap: Bitmap, mode: ChatQuestionMode): ChatResponse {
        val systemInstruction = when (mode) {
            ChatQuestionMode.Translate ->
                CommApplication.appContext.getString(mode.instructionId, PrefAssist.getString(PrefConst.TRANSLATE_TARGET))

            else -> CommApplication.appContext.getString(mode.instructionId)
        }

        val generativeModel = Firebase.vertexAI.generativeModel(
            modelName = Const.AiModelName.GEMINI,
//            apiKey = BuildConfig.GOOGLE_API_KEY,
            // generationConfig = generationConfig { maxOutputTokens = 0 },
            systemInstruction = content { text(systemInstruction + systemPromptRenderMath) },
            generationConfig = generationConfig {
                temperature = if (RconfAssist.getBoolean(RconfConst.IS_NOT_RANDOM_RESPONSE)) 0f else 1f
                topP = if (RconfAssist.getBoolean(RconfConst.IS_NOT_RANDOM_RESPONSE)) 0f else 1f
            }

            )

        try {

            val inputContent = content {
                image(bitmap)
                text(prompt)
            }

            val response = withContext(Dispatchers.IO) {
                generativeModel.generateContent(inputContent)
            }
            val content = response.text
            val promptTokenCount = response.usageMetadata?.promptTokenCount!!
            val candidatesTokenCount = response.usageMetadata?.candidatesTokenCount!!
            val totalTokenCount = response.usageMetadata?.totalTokenCount!!

            val inputTokenCount = PrefAssist.getInt(PrefConst.Token.INPUT_TOKEN_COUNT) + promptTokenCount
            val outputTokenCount = PrefAssist.getInt(PrefConst.Token.OUTPUT_TOKEN_COUNT) + candidatesTokenCount
            val totalTokenCountPref = PrefAssist.getInt(PrefConst.Token.TOTAL_TOKEN_COUNT) + totalTokenCount

            PrefAssist.setInt(PrefConst.Token.INPUT_TOKEN_COUNT, inputTokenCount)
            PrefAssist.setInt(PrefConst.Token.OUTPUT_TOKEN_COUNT, outputTokenCount)
            PrefAssist.setInt(PrefConst.Token.TOTAL_TOKEN_COUNT, totalTokenCountPref)

            return ChatResponse(
                chat = Chat(
                    prompt = content ?: CommApplication.appContext.getString(R.string.errors_please_try_again),
                    bitmap = null,
                    isFromUser = false,
                    isError = false,
                    botType = BotType.BOT_GEMINI
                ),
                inputTokenCount = promptTokenCount,
                outputTokenCount = candidatesTokenCount,
                totalTokenCount = totalTokenCount
            )


        } catch (e: Exception) {
            return ChatResponse(
                chat = Chat(
                    prompt = CommApplication.appContext.getString(R.string.errors_please_try_again),
                    bitmap = null,
                    isFromUser = false,
                    isError = true,
                    botType = BotType.BOT_GEMINI
                ),
                inputTokenCount = 0,
                outputTokenCount = 0,
                totalTokenCount = 0
            )
        }
    }
}