package com.amobilab.ezmath.ai.presentation.ui.coin_history

import amobi.module.compose.foundation.AppBox
import amobi.module.compose.foundation.AppColumn
import amobi.module.compose.foundation.AppDivider
import amobi.module.compose.foundation.AppIcon
import amobi.module.compose.foundation.AppRow
import amobi.module.compose.foundation.AppSpacer
import amobi.module.compose.foundation.AppText
import amobi.module.compose.theme.AppColors
import amobi.module.compose.theme.AppFontFamily
import amobi.module.compose.theme.AppFontSize
import amobi.module.compose.theme.AppSize
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.amobilab.ezmath.ai.R
import com.amobilab.ezmath.ai.data.db.entities.CoinHistoryEntity
import com.amobilab.ezmath.ai.data.db.entities.TransactionType
import com.amobilab.ezmath.ai.presentation.common.shared_components.AppAppbar
import com.amobilab.ezmath.ai.presentation.common.shared_values.AppThemeMode

@Composable
fun CoinTransactionHistoryScreen(innerPaddingHome: PaddingValues, onClick: () -> Unit) {
    val viewModel = hiltViewModel<CoinTransactionHistoryViewModel>()
    val selectedFilter by viewModel.transactionFilter.collectAsState()
    val searchQuery by viewModel.searchQuery.collectAsState()
    val filteredTransactions by viewModel.filteredTransactions.collectAsState(initial = emptyList())
    val allCoinHistory by viewModel.allCoinHistory.collectAsState(initial = emptyList())
    val groupedTransactions by viewModel.groupedTransactionsByDate.collectAsState(initial = emptyMap())
    val isSearching by viewModel.isSearching.collectAsState()
    val searchSuggestions by viewModel.searchSuggestions.collectAsState()
    val showSuggestions by viewModel.showSuggestions.collectAsState()

    val focusRequester = remember { FocusRequester() }

    val currentAppTheme by viewModel.coinViewModel.appThemeMode.observeAsState()
    val isDarkTheme = when (currentAppTheme) {
        AppThemeMode.DARK -> true
        AppThemeMode.LIGHT -> false
        else -> isSystemInDarkTheme() // Only use system theme when in SYSTEM mode
    }

    Scaffold { innerPadding ->
        AppColumn(Modifier.fillMaxSize()) {
            AppAppbar(
                innerPadding = innerPadding,
                title = stringResource(R.string.history_coin),
                onBack = { onClick() }
            )

            // Hiển thị danh sách
            if (allCoinHistory.isEmpty()) {
                AppColumn(
                    modifier = Modifier
                        .fillMaxWidth()
                        .fillMaxHeight(0.7f),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    Image(
                        modifier = Modifier
                            .fillMaxWidth(0.8f),
                        painter = painterResource(
                            id = if (isDarkTheme) {
                                R.drawable.svg_bg_no_coin_history_dark_mode
                            } else {
                                R.drawable.svg_bg_no_coin_history
                            }
                        ),
                        contentDescription = null,
                    )
                    AppSpacer(12.dp)
                    AppText(
                        text = stringResource(R.string.no_history_coin),
                        color = AppColors.current.text,
                        fontSize = 16.sp,
                        lineHeight = 24.sp,
                    )

                    AppSpacer(64.dp)
                }
            } else {
                AppRow(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 8.dp, bottom = 12.dp)
                        .padding(horizontal = 12.dp)
                        .border(
                            width = 1.dp,
                            color = AppColors.current.borderSearch,
                            shape = RoundedCornerShape(8.dp)
                        ),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    AppRow(
                        modifier = Modifier
                            .weight(1f),
                        verticalAlignment = Alignment.CenterVertically

                    ) {
                        AppIcon(
                            R.drawable.ic_search_new,
                            size = 16.dp,
                            tint = AppColors.current.textHintColor,
                        ) { focusRequester.requestFocus() }

                        val interactionSource = remember { MutableInteractionSource() }
                        BasicTextField(
                            modifier = Modifier
                                .weight(1f)
                                .focusRequester(focusRequester),
                            value = searchQuery,
                            onValueChange = viewModel::onSearchQueryChanged,
                            visualTransformation = VisualTransformation.None,
                            interactionSource = interactionSource,
                            textStyle = TextStyle(
                                color = AppColors.current.text,
                                fontSize = AppFontSize.BODY1,
                                fontFamily = AppFontFamily.get(),
                                fontWeight = FontWeight.Medium,
                            ),
                            cursorBrush = SolidColor(AppColors.current.text),
                            enabled = true,
                            singleLine = true,
                        ) { innerTextField ->
                            TextFieldDefaults.DecorationBox(
                                value = searchQuery,
                                visualTransformation = VisualTransformation.None,
                                innerTextField = innerTextField,
                                singleLine = true,
                                enabled = true,
                                interactionSource = interactionSource,
                                contentPadding = PaddingValues(vertical = 12.dp),
                                shape = RoundedCornerShape(12.dp),
                                colors = TextFieldDefaults.colors(
                                    focusedIndicatorColor = Color.Transparent,
                                    unfocusedIndicatorColor = Color.Transparent,
                                    disabledIndicatorColor = Color.Transparent,
                                    focusedContainerColor = Color.Transparent,
                                    unfocusedContainerColor = Color.Transparent,
                                ),
                                placeholder = {
                                    AppText(
                                        text = stringResource(R.string.search),
                                        color = AppColors.current.textHintColor,
                                        fontSize = AppFontSize.BODY2,
                                    )
                                }
                            )
                        }
                    }
                    // DropdownMenu for sorting
                    var expanded by remember { mutableStateOf(false) }
                    AppBox {
                        AppSpacer(
                            modifier = Modifier
                                .padding(6.dp)
                                .size(AppSize.MIN_TOUCH_SIZE - 12.dp)
                                .clip(RoundedCornerShape(10.dp))
                        )
                        AppIcon(
                            R.drawable.ic_search_filter,
                            size = 20.dp,
                            tint = AppColors.current.textHintColor,
                        ) { expanded = true }

                        DropdownMenu(
                            expanded = expanded,
                            onDismissRequest = { expanded = false },
                            shape = RoundedCornerShape(8.dp),
                            modifier = Modifier
                                .background(AppColors.current.backgroundDropdownMenu) //color
                        ) {
                            DropdownMenuItem(
                                onClick = {
                                    viewModel.onFilterChanged(TransactionFilterType.ALL)
                                    expanded = false
                                },
                                text = {
                                    AppRow(
                                        modifier = Modifier.fillMaxWidth(),
                                        horizontalArrangement = Arrangement.SpaceBetween,
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        AppText(
                                            text = stringResource(R.string.days),
                                            fontSize = AppFontSize.BODY1,
                                            fontWeight = FontWeight.W400,
                                            lineHeight = 20.sp,
                                            color = AppColors.current.titleText
                                        )
                                        AppSpacer(4.dp)

                                        if (selectedFilter == TransactionFilterType.ALL) {
                                            AppIcon(
                                                R.drawable.svg_comm_ic_check,
                                                size = 24.dp,
                                                tint = AppColors.current.titleText,
                                            )
                                        }
                                    }
                                }
                            )
                            DropdownMenuItem(
                                onClick = {
                                    viewModel.onFilterChanged(TransactionFilterType.SPEND)
                                    expanded = false
                                },
                                text = {
                                    AppRow(
                                        modifier = Modifier.fillMaxWidth(),
                                        horizontalArrangement = Arrangement.SpaceBetween,
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        AppText(
                                            text = stringResource(R.string.used_coins),
                                            fontSize = AppFontSize.BODY1,
                                            fontWeight = FontWeight.W400,
                                            lineHeight = 20.sp,
                                            color = AppColors.current.titleText
                                        )
                                        AppSpacer(4.dp)

                                        if (selectedFilter == TransactionFilterType.SPEND) {
                                            AppIcon(
                                                R.drawable.svg_comm_ic_check,
                                                size = 24.dp,
                                                tint = AppColors.current.titleText,
                                            )
                                        }
                                    }
                                }
                            )
                            DropdownMenuItem(
                                onClick = {
                                    viewModel.onFilterChanged(TransactionFilterType.TOP_UP)
                                    expanded = false
                                },
                                text = {
                                    AppRow(
                                        modifier = Modifier.fillMaxWidth(),
                                        horizontalArrangement = Arrangement.SpaceBetween,
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        AppText(
                                            text = stringResource(R.string.top_up_coins),
                                            fontSize = AppFontSize.BODY1,
                                            fontWeight = FontWeight.W400,
                                            lineHeight = 20.sp,
                                            color = AppColors.current.titleText
                                        )
                                        AppSpacer(4.dp)

                                        if (selectedFilter == TransactionFilterType.TOP_UP) {
                                            AppIcon(
                                                R.drawable.svg_comm_ic_check,
                                                size = 24.dp,
                                                tint = AppColors.current.titleText,
                                            )
                                        }
                                    }
                                }
                            )
                        }
                    }
                }
                LazyColumn(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(MaterialTheme.colorScheme.background)
                ) {
                    if (selectedFilter == TransactionFilterType.ALL) {
                        groupedTransactions.toSortedMap(compareByDescending { it }).forEach { (date, transactionsInDate) ->
                            item {
                                AppText(
                                    text = date,
                                    fontSize = AppFontSize.BODY2,
                                    lineHeight = 20.sp,
                                    fontWeight = FontWeight.W400,
                                    color = AppColors.current.titleContent,
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(horizontal = 16.dp)
                                        .padding(start = 12.dp)
                                )
                                Box(
                                    modifier = Modifier
                                        .padding(horizontal = 12.dp, vertical = 4.dp)
                                        .fillMaxWidth()
                                        .clip(RoundedCornerShape(12.dp))
                                        .background(AppColors.current.backgroundContent)
                                        .padding(12.dp)
                                ) {
                                    AppColumn {
                                        transactionsInDate.sortedByDescending { it.date }.forEachIndexed { index, transaction ->
                                            CoinTransactionItem(transaction)
                                            if (index < transactionsInDate.lastIndex) {
                                                AppRow {
                                                    AppSpacer(12.dp)
                                                    AppDivider()
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }


                    } else {
                        items(filteredTransactions.reversed()) { transaction ->
                            // Box bọc danh sách giao dịch với nền
                            Box(
                                modifier = Modifier
                                    .padding(horizontal = 12.dp, vertical = 4.dp)
                                    .fillMaxWidth()
                                    .clip(RoundedCornerShape(12.dp))
                                    .background(AppColors.current.backgroundContent)
                                    .padding(12.dp)
                            ) {
                                CoinTransactionItem(transaction)
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun CoinTransactionItem(transaction: CoinHistoryEntity) {
    val viewModel = hiltViewModel<CoinTransactionHistoryViewModel>()

    AppColumn(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(10.dp)),
    ) {
        AppRow(
            modifier = Modifier
                .fillMaxWidth()
                .padding(4.dp),
        ) {
            Image(
                modifier = Modifier
                    .size(20.dp),
                painter = painterResource(
                    id = if (transaction.type == TransactionType.EARN) R.drawable.ic_arrow_down_left
                    else R.drawable.ic_arrow_upper_right
                ),
                contentDescription = null,
            )
            AppSpacer(8.dp)
            AppColumn(modifier = Modifier.fillMaxWidth()) {
                AppRow(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    AppText(
                        text = (if (transaction.type == TransactionType.EARN) stringResource(R.string.txtid_coin_in) else stringResource(R.string.txtid_coin_out)),
                        fontSize = AppFontSize.BODY1,
                        fontWeight = FontWeight.W500,
                        lineHeight = 24.sp,
                        color = AppColors.current.text
                    )
                    AppRow(
                        horizontalArrangement = Arrangement.Center,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        AppText(
                            text = "${if (transaction.type == TransactionType.EARN) "+" else "-"}${transaction.amount}",
                            fontSize = AppFontSize.BODY1,
                            fontWeight = FontWeight.W600,
                            lineHeight = 24.sp,
                            color = AppColors.current.text
                        )
                        AppSpacer(4.dp)
                        Image(
                            modifier = Modifier.size(20.dp),
                            painter = painterResource(id = R.drawable.svg_ic_coin_2),
                            contentDescription = null,
                        )
                    }
                }
                AppRow(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    AppText(
                        text = "${viewModel.formatTime(transaction.date)} • ${transaction.description}",
                        color = AppColors.current.textHintItemCoinHistory,
                        fontSize = AppFontSize.BODY2,
                        fontWeight = FontWeight.W400,
                        lineHeight = 20.sp,
                    )
                }
            }
        }
    }
}
