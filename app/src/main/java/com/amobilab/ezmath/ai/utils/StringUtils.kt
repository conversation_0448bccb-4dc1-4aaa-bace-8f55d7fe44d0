package com.amobilab.ezmath.ai.utils

object StringUtils {
    /**
     * Removes diacritics (accents) from Vietnamese text
     * @param text The text to normalize
     * @return Text with diacritics removed
     */
    fun normalizeVietnamese(text: String): String {
        return text
            .replace("[àáạảãâầấậẩẫăằắặẳẵ]".toRegex(), "a")
            .replace("[èéẹẻẽêềếệểễ]".toRegex(), "e")
            .replace("[ìíịỉĩ]".toRegex(), "i")
            .replace("[òóọỏõôồốộổỗơờớợởỡ]".toRegex(), "o")
            .replace("[ùúụủũưừứựửữ]".toRegex(), "u")
            .replace("[ỳýỵỷỹ]".toRegex(), "y")
            .replace("[đ]".toRegex(), "d")
            .replace("[ÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴ]".toRegex(), "A")
            .replace("[ÈÉẸẺẼÊỀẾỆỂỄ]".toRegex(), "E")
            .replace("[ÌÍỊỈĨ]".toRegex(), "I")
            .replace("[ÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠ]".toRegex(), "O")
            .replace("[ÙÚỤỦŨƯỪỨỰỬỮ]".toRegex(), "U")
            .replace("[ỲÝỴỶỸ]".toRegex(), "Y")
            .replace("[Đ]".toRegex(), "D")
    }
}