package com.amobilab.ezmath.ai.presentation.common.shared_values

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import com.amobilab.ezmath.ai.R

enum class ModelAiMode(
    @DrawableRes val iconRes: Int,
    @DrawableRes val iconResNoFrame: Int,
    @StringRes val titleRes: Int,
    @StringRes val contentRes: Int
) {
    GPT(
        iconRes = R.drawable.ic_chat_gpt,
        iconResNoFrame = R.drawable.ic_chat_gpt_no_frame,
        titleRes = R.string.chat_gpt,
        contentRes = R.string.smart_fast_creative
    ),
    GEMINI(
        iconRes = R.drawable.ic_gemini,
        iconResNoFrame = R.drawable.ic_chat_gemini_no_frame,
        titleRes = R.string.gemini,
        contentRes = R.string.deep_versatile_powerful
    )
}