package com.amobilab.ezmath.ai.presentation.common.share_dialog

import amobi.module.compose.extentions.AppPreview
import amobi.module.compose.foundation.AppButton
import amobi.module.compose.foundation.AppColumn
import amobi.module.compose.foundation.AppDivider
import amobi.module.compose.foundation.AppRow
import amobi.module.compose.foundation.AppSpacer
import amobi.module.compose.foundation.AppText
import amobi.module.compose.theme.AppColors
import amobi.module.compose.theme.AppFontSize
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.TextField
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.amobilab.ezmath.ai.R

@AppPreview
@Composable
fun CommonDialogPreview() {
    CommonDialog(
        title = "Title",
        message = "This is a message",
        confirmText = stringResource(R.string.txtid_ok),
        dismissText = stringResource(R.string.txtid_cancel),
        onConfirm = {},
        onDismiss = {}
    )
}


@Composable
fun CommonDialog(
    title: String,
    message: String = "",
    textState: MutableState<TextFieldValue> = remember {
        mutableStateOf(TextFieldValue(""))
    },
    isEditMode: Boolean = false,
    confirmText: String = stringResource(R.string.txtid_ok),
    dismissText: String = stringResource(R.string.txtid_cancel),
    onConfirm: () -> Unit,
    onDismiss: () -> Unit
) {

    val focusRequester = remember { FocusRequester() }

    val gradientBrush = Brush.linearGradient(
        colors = listOf(Color(0xFF81D8D0), Color(0xFF0ABAB5)),
        start = Offset(0f, 0f),
        end = Offset(0f, 100f)
    )

    Dialog(
        properties = DialogProperties(usePlatformDefaultWidth = false),
        onDismissRequest = { onDismiss() },
    ) {
        Card(
            modifier = Modifier.wrapContentSize().padding(horizontal = 16.dp),
            shape = RoundedCornerShape(12.dp),
            colors = CardDefaults.cardColors(containerColor = AppColors.current.dialogBackground),
        ) {
            AppColumn(
                modifier = Modifier
                    .fillMaxWidth()
            ) {
                AppSpacer(12.dp)
                // Header
                AppRow(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(48.dp)
                        .background(AppColors.current.dialogBackground)
                        .padding(horizontal = 16.dp),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Center
                ) {
                    AppText(
                        text = title,
                        fontSize = AppFontSize.TITLE2,
                        lineHeight = 28.sp,
                        color = AppColors.current.text,
                        fontWeight = FontWeight.Bold,
                    )
                }

                AppDivider()

                // Content
                AppColumn(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp, vertical = 12.dp),
                    verticalArrangement = Arrangement.spacedBy(12.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    if (isEditMode) {
                        // TextField with clear button
                        Box {
                            TextField(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .background(AppColors.current.dialogBackground)
                                    .focusRequester(focusRequester),
                                value = textState.value,
                                onValueChange = { textState.value = it },
                                textStyle = TextStyle.Default.copy(
                                    fontSize = AppFontSize.BODY1,
                                    color = AppColors.current.text
                                ),
                                shape = RoundedCornerShape(8.dp),
                                singleLine = true,
                                colors = TextFieldDefaults.colors(
                                    focusedIndicatorColor = Color.Transparent,
                                    unfocusedIndicatorColor = Color.Transparent,
                                    disabledIndicatorColor = Color.Transparent,
                                    focusedContainerColor = AppColors.current.focusedContainerColor, //color
                                    unfocusedContainerColor = AppColors.current.unfocusedContainerColor, //color
                                    cursorColor = AppColors.current.text,
                                    ),
                                trailingIcon = {
                                    if (textState.value.text.isNotEmpty()) {
                                        IconButton(onClick = { textState.value = TextFieldValue("") }) {
                                            Icon(
                                                painter = painterResource(id = R.drawable.ic_edit_text_remove),
                                                contentDescription = "Clear",
                                                tint = Color.Unspecified
                                            )
                                        }
                                    }
                                },
                            )
                        }
                    } else {
                        // Normal message
                        AppText(
                            text = message,
                            fontSize = AppFontSize.BODY1,
                            color = AppColors.current.text,
                            lineHeight = 24.sp,
                            fontWeight = FontWeight.W400,
                            textAlign = TextAlign.Center
                        )
                    }

                    AppSpacer(12.dp)

                    // Buttons
                    AppRow(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        OutlinedButton(
                            modifier = Modifier.weight(1f).height(48.dp),
                            colors = ButtonDefaults.outlinedButtonColors(
                                contentColor = AppColors.current.buttonActive,
                                containerColor = Color.Transparent,
                            ),
                            border = BorderStroke(1.dp, AppColors.current.buttonActive),
                            shape = RoundedCornerShape(8.dp),
                            onClick = { onDismiss() }
                        ) {
                            AppText(
                                text = dismissText,
                                fontSize = AppFontSize.BODY1,
                                maxLines = 1,
                                overflow = TextOverflow.Ellipsis,
                                fontWeight = FontWeight.Bold,
                                color = AppColors.current.buttonActive
                            )
                        }

                        Spacer(modifier = Modifier.width(12.dp))

                        AppButton(
                            modifier = Modifier.weight(1f).height(48.dp),
                            onClick = { onConfirm() },
                            colors = ButtonDefaults.buttonColors(
                                containerColor = Color.Transparent,
                                contentColor = AppColors.current.onText
                            ),
                            shape = RoundedCornerShape(8.dp),
                            contentPadding = PaddingValues(0.dp)
                        ) {
                            Box(
                                modifier = Modifier
                                    .fillMaxSize()
                                    .background(brush = gradientBrush, shape = RoundedCornerShape(8.dp))
                                    .padding(horizontal = 16.dp, vertical = 12.dp),
                                contentAlignment = Alignment.Center
                            ) {
                                AppText(
                                    text = confirmText,
                                    fontSize = AppFontSize.BODY1,
                                    fontWeight = FontWeight.W500,
                                    color = AppColors.current.onText,
                                    lineHeight = 24.sp
                                )
                            }
                        }
                    }
                }
                AppSpacer(12.dp)
            }
        }
    }
    LaunchedEffect(Unit) {
        if (isEditMode) {
            focusRequester.requestFocus()
            textState.value = textState.value.copy(
                selection = TextRange(textState.value.text.length)
            )
        }
    }
}
