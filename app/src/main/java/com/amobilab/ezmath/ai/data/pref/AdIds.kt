package com.amobilab.ezmath.ai.data.pref

import amobi.module.common.configs.RconfAssist


object AdIds {
    const val AD_ID_ROOT = "ca-app-pub-7667495987617321/"

    val REWARD_CREDIT = arrayOf(AD_ID_ROOT + "6777353887")

    const val INTER_OPEN_HI = AD_ID_ROOT + "3839592485"
    const val INTER_OPEN_ALL = AD_ID_ROOT + "1576391228"
    fun interOpen(): Array<String> {
        return if (RconfAssist.getBoolean(RconfConst.IS_AD_MULTI_IDS))
            arrayOf(
                INTER_OPEN_HI, // Hi
                INTER_OPEN_ALL  // All
            )
        else
            arrayOf(INTER_OPEN_ALL)
    }

    const val INTER_ACTION_HI = AD_ID_ROOT + "1062461303"
    const val INTER_ACTION_ALL = AD_ID_ROOT + "5128864678"
    fun interAction(): Array<String> {
        return if (RconfAssist.getBoolean(RconfConst.IS_AD_MULTI_IDS))
            arrayOf(
                INTER_ACTION_HI, // Hi
                INTER_ACTION_ALL  // All
            )
        else
            arrayOf(INTER_ACTION_ALL)
    }

    const val BANNER_GENERAL_HI = AD_ID_ROOT + "7998329340"
    const val BANNER_GENERAL_ALL = AD_ID_ROOT + "9311411015"
    fun bannerGeneral(): Array<String> {
        return if (RconfAssist.getBoolean(RconfConst.IS_AD_MULTI_IDS))
            arrayOf(
                BANNER_GENERAL_HI, // Hi
                BANNER_GENERAL_ALL  // All
            )
        else
            arrayOf(BANNER_GENERAL_ALL)
    }
}