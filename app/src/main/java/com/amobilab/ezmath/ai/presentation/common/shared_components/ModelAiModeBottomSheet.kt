package com.amobilab.ezmath.ai.presentation.common.shared_components

import amobi.module.common.configs.PrefAssist
import amobi.module.common.utils.debugLog
import amobi.module.compose.foundation.AppBox
import amobi.module.compose.foundation.AppColumn
import amobi.module.compose.foundation.AppRadioButton
import amobi.module.compose.foundation.AppRow
import amobi.module.compose.foundation.AppText
import amobi.module.compose.theme.AppColors
import amobi.module.compose.theme.AppFontSize
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.RadioButtonDefaults
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.amobilab.ezmath.ai.R
import com.amobilab.ezmath.ai.data.pref.PrefConst
import com.amobilab.ezmath.ai.presentation.common.shared_values.ModelAiMode
import kotlinx.coroutines.launch

@Composable
fun ModelAiModeBottomSheet(
    showIt: Boolean,
    onDismissRequest: (ModelAiMode?) -> Unit, // ✅ Đổi sang nullable để phân biệt dismiss không chọn
) {
    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)
    val coroutineScope = rememberCoroutineScope()

    if (showIt) {
        ModalBottomSheet(
            onDismissRequest = {
                coroutineScope.launch {
                    sheetState.hide()
                    onDismissRequest(null) // ✅ dismiss không chọn mode
                }
            },
            sheetState = sheetState,
            dragHandle = null,
            containerColor = AppColors.current.bottomSheetBackground
        ) {
            BottomSheetSelectModelAI { selectedMode ->
                coroutineScope.launch {
                    sheetState.hide()
                    onDismissRequest(selectedMode) // ✅ chọn xong mới gọi callback
                    PrefAssist.setString(PrefConst.MODEL_AI, selectedMode.name)
                }
            }
        }
    }
}


@Composable
private fun BottomSheetSelectModelAI(
    onDismiss: (ModelAiMode) -> Unit = {}
) {
    val selectedModelString by remember { mutableStateOf(PrefAssist.getString(PrefConst.MODEL_AI)) }

    val selectedOption =
        when (selectedModelString) {
            ModelAiMode.GPT.name -> ModelAiMode.GPT
            ModelAiMode.GEMINI.name -> ModelAiMode.GEMINI
            else -> ModelAiMode.GPT
        }
    AppColumn(
        modifier = Modifier.fillMaxWidth().padding(bottom =  12.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        var _selectedOption by remember { mutableStateOf(selectedOption) }

        Spacer(modifier = Modifier.height(16.dp))

        // Header
        ModelAiModeHeader()

        Spacer(modifier = Modifier.height(16.dp))
        HorizontalDivider(
            modifier = Modifier
                .fillMaxWidth(),
            thickness = 1.dp,
            color = AppColors.current.divider2
        )
        Spacer(modifier = Modifier.height(16.dp))

        // Options list
        AppColumn(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            ModelAiMode.entries.forEach { type ->
                ModelAiModeOption(
                    title = if (type.name == ModelAiMode.GEMINI.name){"Gemini"} else stringResource(type.titleRes),
                    subtitle = stringResource(type.contentRes),
                    iconContent = type.iconRes,
                    isSelected = _selectedOption == type,
                    onClick = {
                        _selectedOption = type
                        onDismiss(_selectedOption)
                        debugLog("_selectedOption $_selectedOption")
                    }
                )
            }
        }
        Spacer(modifier = Modifier.height(16.dp))
    }
}

@Composable
fun ModelAiModeHeader() {
    AppColumn(modifier = Modifier.fillMaxWidth()) {
        AppBox(
            modifier = Modifier
                .fillMaxWidth()
        ) {
            // Title
            AppText(
                text = stringResource(R.string.select_ai_model),
                color = AppColors.current.text,
                fontSize = AppFontSize.BODY1,
                fontWeight = FontWeight.W700,
                lineHeight = 24.sp,
                modifier = Modifier.align(Alignment.Center)
            )
        }
    }
}

@Composable
fun ModelAiModeOption(
    title: String,
    subtitle: String,
    iconContent: Int,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    AppRow(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(12.dp))
            .background(AppColors.current.bottomSheetContentBackground)
            .clickable { onClick() }
            .padding(start = 16.dp, end = 12.dp, top = 16.dp, bottom = 16.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        // Icon and Text
        AppRow(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // Icon with gradient background
            Image(
                painter  = painterResource(id = iconContent),
                contentDescription = null,
                modifier = Modifier.size(36.dp)
            )

            // Title and subtitle
            AppColumn(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                AppText(
                    text = title,
                    color = AppColors.current.bottomSheetContentTitle,
                    fontSize = AppFontSize.BODY2,
                    fontWeight = FontWeight.W700,
                    lineHeight = 20.sp
                )

                AppText(
                    text = subtitle,
                    color = AppColors.current.bottomSheetContentHint,
                    fontSize = AppFontSize.BODY2,
                    fontWeight = FontWeight.W400,
                    lineHeight = 20.sp,
                    maxLines = 2
                )
            }
            // Radio button
            AppRadioButton(
                onClick = { onClick() },
                selected = isSelected,
                colors = RadioButtonDefaults.colors(
                    selectedColor = AppColors.current.radioButtonActive,
                    unselectedColor = AppColors.current.radioButtonInactive,
                ),
            )
        }
    }
}