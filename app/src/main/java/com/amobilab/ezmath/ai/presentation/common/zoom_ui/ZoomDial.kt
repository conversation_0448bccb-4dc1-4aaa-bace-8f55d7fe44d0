package com.amobilab.ezmath.ai.presentation.common.zoom_ui

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.nativeCanvas
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import kotlin.math.cos
import kotlin.math.sin

@Composable
fun ZoomDial(
    modifier: Modifier = Modifier,
    zoomLevels: List<Float> = listOf(0.5f, 1f, 2f, 3.4f, 5f, 10f),
    currentZoom: Float,
    onZoomChange: (Float) -> Unit
) {
    val sweepAngle = 270f
    val totalSegments = zoomLevels.size - 1
    val centerZoomIndex = zoomLevels.indexOf(currentZoom).coerceAtLeast(0)
    val rotationAngle = -sweepAngle * (centerZoomIndex.toFloat() / totalSegments) + 135f

    Box(
        modifier = modifier,
        contentAlignment = Alignment.Center
    ) {
        // Vòng cung được xoay
        Canvas(modifier = Modifier
            .fillMaxSize()
            .graphicsLayer {
                rotationZ = rotationAngle
            }
        ) {
            val centerX = size.width / 2
            val centerY = size.height
            val radius = size.minDimension / 2.2f

            // Vẽ vòng cung
            drawArc(
                color = Color.Gray,
                startAngle = 135f,
                sweepAngle = sweepAngle,
                useCenter = false,
                style = Stroke(6.dp.toPx())
            )

            // Vạch chia và nhãn zoom
            for (i in zoomLevels.indices) {
                val fraction = i.toFloat() / totalSegments
                val angle = 135f + sweepAngle * fraction
                val rad = Math.toRadians(angle.toDouble())

                val outer = Offset(
                    x = centerX + radius * cos(rad).toFloat(),
                    y = centerY + radius * sin(rad).toFloat()
                )
                val inner = Offset(
                    x = centerX + (radius - 20.dp.toPx()) * cos(rad).toFloat(),
                    y = centerY + (radius - 20.dp.toPx()) * sin(rad).toFloat()
                )
                drawLine(Color.LightGray, inner, outer, strokeWidth = 2.dp.toPx())

                // Nhãn zoom
                val label = "${zoomLevels[i]}x"
                val textAngle = rad
                val labelOffset = Offset(
                    x = centerX + (radius - 40.dp.toPx()) * cos(textAngle).toFloat(),
                    y = centerY + (radius - 40.dp.toPx()) * sin(textAngle).toFloat()
                )

                drawContext.canvas.nativeCanvas.drawText(
                    label,
                    labelOffset.x,
                    labelOffset.y,
                    android.graphics.Paint().apply {
                        color = android.graphics.Color.WHITE
                        textSize = 30.sp.toPx()
                        textAlign = android.graphics.Paint.Align.CENTER
                        isFakeBoldText = true
                    }
                )
            }
        }

        // Chấm đỏ cố định ở trên (con trỏ)
        Canvas(modifier = Modifier
            .fillMaxSize()
        ) {
            val centerX = size.width / 2
            val centerY = size.height
            val pointerRadius = size.minDimension / 2.2f - 10.dp.toPx()

            val pointerX = centerX
            val pointerY = centerY - pointerRadius

            drawCircle(
                color = Color.Red,
                radius = 10.dp.toPx(),
                center = Offset(pointerX, pointerY)
            )
        }
    }
}
