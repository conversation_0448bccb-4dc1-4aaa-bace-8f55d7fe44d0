package com.amobilab.ezmath.ai.presentation.navigation

import amobi.module.common.utils.dlog
import amobi.module.common.views.CommActivity
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.navigation.NavHostController
import dagger.hilt.android.scopes.ActivityRetainedScoped
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@ActivityRetainedScoped
class AppNavigator @Inject constructor() {

    private val _navEvents = Channel<Direction>(Channel.UNLIMITED)
    val navEvents: Flow<Direction> = _navEvents.receiveAsFlow()

    fun navigateTo(appNavScreen: ScreenRoutes) {
        _navEvents.trySend(Direction.NavigateTo(appNavScreen))
    }

    fun navigateBack() {
        _navEvents.trySend(Direction.NavigateBack)
    }

    fun popBackStackAndNavigate(newDestinationRoute: String) {
        _navEvents.trySend(Direction.PopBackStackAndNavigate(newDestinationRoute))
    }

    sealed interface Direction {
        data class NavigateTo(
            val navDirections: ScreenRoutes,
        ) : Direction

        object NavigateBack : Direction

        data class PopBackStackAndNavigate(
            val newDestinationRoute: String
        ) : Direction
    }
}

fun AppNavigator.setupWithNavController(
    activity: CommActivity, navController: NavHostController
) {
    activity.lifecycleScope.launch {
        activity.repeatOnLifecycle(Lifecycle.State.STARTED) {
            navEvents.collect { direction ->
                when (direction) {
                    is AppNavigator.Direction.NavigateTo -> {
                        navController.navigate(direction.navDirections)
                    }

                    is AppNavigator.Direction.NavigateBack -> {
                        if (!navController.popBackStack()) {
                            activity.finish()
                        }
                    }
                    is AppNavigator.Direction.PopBackStackAndNavigate -> {
                        try {
                            // Navigate to the new destination while clearing the back stack
                            navController.navigate(direction.newDestinationRoute) {
                                // Pop up to the start destination of the graph to
                                // avoid building up a large stack of destinations
                                popUpTo(navController.graph.startDestinationId) {
                                    // Reset the back stack completely
                                    inclusive = true
                                }
                            }
                        } catch (e: Exception) {
                            dlog("PopBackStackAndNavigate error: ${e.message}")
                            // Fall back to TabScreen if navigation fails
                            try {
                                navController.navigate(ScreenRoutes.HomeScreen()) {
                                    popUpTo(navController.graph.id) {
                                        inclusive = true
                                    }
                                }
                            } catch (e2: Exception) {
                                dlog("Fallback navigation also failed: ${e2.message}")
                            }
                        }
                    }
                }
            }
        }
    }
}