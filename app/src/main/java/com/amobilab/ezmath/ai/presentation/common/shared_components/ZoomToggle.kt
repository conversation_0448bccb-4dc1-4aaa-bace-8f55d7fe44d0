package com.amobilab.ezmath.ai.presentation.common.shared_components

import amobi.module.compose.foundation.AppBox
import amobi.module.compose.foundation.AppRow
import amobi.module.compose.foundation.AppText
import amobi.module.compose.theme.AppFontSize
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

@Composable
fun ZoomToggle(
    modifier: Modifier = Modifier,
    selectedIndex: Float,
    onSelectedChange: (Float) -> Unit
) {
    val cornerRadius = 20.dp
    val backgroundColor = Color(0xFF000000).copy(alpha = 0.4f)
    val selectedColor = Color(0xFF000000).copy(alpha = 0.5f)
    val unselectedColor = Color(0xFFE6C083)

    // Định nghĩa các mức zoom
    val zoomLevels = listOf(1f, 2f, 5f)

    // Tìm mức zoom gần nhất với selectedIndex (sử dụng trực tiếp giá trị zoom ratio)
    val closestZoomIndex = when {
        selectedIndex <= 1.5f -> 0 // 1x
        selectedIndex <= 3.5f -> 1 // 2x
        else -> 2 // 5x
    }

    AppRow(
        modifier = modifier
            .clip(CircleShape)
            .background(backgroundColor)
            .padding(horizontal = 4.dp, vertical = 4.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Button 1 - 1x
        ZoomButton(
            isSelected = closestZoomIndex == 0,
            selectedColor = selectedColor,
            unselectedColor = unselectedColor,
            displayText = "1x",
            onLongPress = { onSelectedChange(-1f) },
            onClick = { onSelectedChange(0f) },
            modifier = Modifier.weight(1f)
        )

        // Button 2 - 2x
        ZoomButton(
            isSelected = closestZoomIndex == 1,
            selectedColor = selectedColor,
            unselectedColor = unselectedColor,
            displayText = "2x",
            onLongPress = { onSelectedChange(-1f) },
            onClick = { onSelectedChange(1f) },
            modifier = Modifier.weight(1f)
        )

        // Button 3 - 5x
        ZoomButton(
            isSelected = closestZoomIndex == 2,
            selectedColor = selectedColor,
            unselectedColor = unselectedColor,
            displayText = "5x",
            onLongPress = { onSelectedChange(-1f) },
            onClick = { onSelectedChange(5f) },
            modifier = Modifier.weight(1f)
        )
    }
}


@Composable
private fun ZoomButton(
    isSelected: Boolean,
    selectedColor: Color,
    unselectedColor: Color,
    displayText: String,
    onLongPress: () -> Unit,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    AppBox(
        modifier = modifier
            .size(48.dp)               // Kích thước vuông để thành hình tròn đúng
            .clip(CircleShape)         // Bo tròn thành hình tròn
            .background(if (isSelected) selectedColor else Color.Transparent)
            .pointerInput(Unit) {
                detectTapGestures(
                    onLongPress = { onLongPress() },
                    onPress = {
                        val released = tryAwaitRelease()
                        if (released) onClick()
                    }
                )
            },
        contentAlignment = Alignment.Center
    ) {
        AppText(
            text = displayText,
            color = if (isSelected) unselectedColor else Color.White,
            fontWeight = FontWeight.W500,
            fontSize = AppFontSize.BODY2,
            lineHeight = 20.sp
        )
    }
}