package com.amobilab.ezmath.ai.presentation.common.shared_components

import amobi.module.compose.foundation.AppBox
import amobi.module.compose.foundation.AppButton
import amobi.module.compose.foundation.AppGlideImage
import amobi.module.compose.foundation.AppText
import amobi.module.compose.foundation.AppTextAutoSize
import amobi.module.compose.theme.AppColors
import amobi.module.compose.theme.AppFontSize
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.amobilab.ezmath.ai.R
import com.amobilab.ezmath.ai.presentation.common.shared_viewmodels.NavigatorViewModel
import com.amobilab.ezmath.ai.presentation.navigation.ScreenRoutes
import com.bumptech.glide.integration.compose.ExperimentalGlideComposeApi


// Data class để chứa thông tin từng thẻ
data class FeatureData(
    val title: String,
    val subtitle: String,
    val image: Int,
    val backGround: Color,
    val startColorButton: Color,
    val endColorButton: Color
)

@Composable
fun HorizontalScrollFeatureCard(
    modifier: Modifier = Modifier,
) {
    val navigatorViewModel = NavigatorViewModel.getInstance()

    // Store string resources in local variables inside the composable
    val literatureTitle = stringResource(R.string.feature_card_literature)
    val writeEssayTitle = stringResource(R.string.feature_card_write_an_essay)
    val researchTitle = stringResource(R.string.feature_card_research_and_analysis)

    val items = listOf(
        FeatureData(
            title = literatureTitle,
            subtitle = stringResource(R.string.feature_card_analysis_of_literary_works_stories_poems_plays),
            image = R.drawable.svg_feature_literature,
            backGround = Color(0xFFE0F7FA),
            startColorButton = Color(0xFF81D8D0),
            endColorButton = Color(0xFF0ABAB5)
        ),
        FeatureData(
            title = writeEssayTitle,
            subtitle = stringResource(R.string.feature_card_let_ai_assist_you_from_start_to_finish),
            image = R.drawable.svg_feature_write_an_essay,
            backGround = Color(0xFFFFF7E1),
            startColorButton = Color(0xFFFF9500),
            endColorButton = Color(0xFFFFDC16)
        ),
        FeatureData(
            title = researchTitle,
            subtitle = stringResource(R.string.feature_card_find_evalute_interpret_and_visualize_information),
            image = R.drawable.svg_feature_research_and_analysis,
            backGround = Color(0xFFE4F2FF),
            startColorButton = Color(0xFF3B82F6),
            endColorButton = Color(0xFF54AFFF)
        )
    )

    LazyRow(
        modifier = Modifier
            .fillMaxWidth(),
        contentPadding = PaddingValues(end = 24.dp),
        horizontalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        items(items = items) { feature ->
            FeatureCard(
                feature = feature,
                modifier = Modifier.fillParentMaxWidth(0.9f)
            ){
                when (feature.title) {
                    literatureTitle -> {
                        navigatorViewModel.navigateTo(ScreenRoutes.Literature())
                    }
                    writeEssayTitle -> {
                        navigatorViewModel.navigateTo(ScreenRoutes.WriteAnEssay())
                    }
                    researchTitle -> {
                        navigatorViewModel.navigateTo(ScreenRoutes.ResearchAndAnalysis())
                    }
                }
            }
        }
    }
}

@OptIn(ExperimentalGlideComposeApi::class)
@Composable
fun FeatureCard(
    feature: FeatureData,
    modifier: Modifier = Modifier,
    onClick: ( ) -> Unit = {}
) {
    val context = LocalContext.current
    val gradientBrush = Brush.linearGradient(
        colors = listOf(feature.startColorButton, feature.endColorButton),
        start = Offset(0f, 0f),
        end = Offset(0f, 100f)
    )
    Card(
        modifier = modifier.height(132.dp),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = feature.backGround),
//        elevation = CardDefaults.cardElevation(4.dp)
    ) {
        AppBox(
            modifier = Modifier.fillMaxSize(),
        ) {
            // Hình ảnh minh họa
            AppGlideImage(
                resId = feature.image,
                modifier = Modifier
                    .fillMaxHeight(0.6f)
                    .clip(RoundedCornerShape(12.dp))
                    .align(Alignment.BottomEnd),
            )
            Column(modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)) {
                AppTextAutoSize(
                    modifier = Modifier.fillMaxWidth(0.8f),
                    text = feature.title,
                    fontSize = AppFontSize.BODY1,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF132537),//Color
                    lineHeight = 24.sp,
                    maxLines = 1,
                    overflow = androidx.compose.ui.text.style.TextOverflow.Ellipsis
                )
                AppTextAutoSize(
                    modifier = Modifier.fillMaxWidth(0.8f),
                    text = feature.subtitle,
                    fontSize = AppFontSize.BODY2,
                    color = Color(0xFF566573),//Color
                    lineHeight = 20.sp,
                    maxLines = 2,
                    overflow = androidx.compose.ui.text.style.TextOverflow.Ellipsis
                )
                Spacer(modifier = Modifier.weight(1f))

                AppButton(
                    modifier = Modifier
                        .height(32.dp)
                        .background(
                            brush = gradientBrush,
                            shape = RoundedCornerShape(8.dp)
                        ),
                    onClick = {
                        onClick()
                    },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color.Transparent,
                        contentColor = AppColors.current.onText
                    ),
                    shape = RoundedCornerShape(8.dp),
                    enabled = true,
                    contentPadding = PaddingValues(0.dp)
                ) {
                    AppBox(
                        modifier = Modifier.padding(horizontal = 16.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        AppText(
                            text = stringResource(R.string.try_now),
                            fontSize = AppFontSize.SMALL,
                            fontWeight = FontWeight.W700,
                            color = AppColors.current.onText,
                            lineHeight = 16.sp
                        )
                    }
                }
            }
        }
    }
}