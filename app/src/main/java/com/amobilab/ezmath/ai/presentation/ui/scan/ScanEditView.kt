package com.amobilab.ezmath.ai.presentation.ui.scan

import amobi.module.common.configs.PrefAssist
import amobi.module.common.utils.debugLog
import amobi.module.compose.extentions.AppPreview
import amobi.module.compose.extentions.PreviewAssist
import amobi.module.compose.foundation.AppColumn
import amobi.module.compose.foundation.AppColumnCentered
import amobi.module.compose.foundation.AppGlideImage
import amobi.module.compose.foundation.AppIcon
import amobi.module.compose.foundation.AppRow
import amobi.module.compose.foundation.AppRowCentered
import amobi.module.compose.foundation.AppSpacer
import amobi.module.compose.foundation.AppText
import amobi.module.compose.theme.AppColors
import amobi.module.compose.theme.AppFontSize
import amobi.module.compose.theme.AppSize
import android.graphics.RectF
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.compose.ui.zIndex
import com.amobilab.ezmath.ai.R
import com.amobilab.ezmath.ai.app.CoinViewModel
import com.amobilab.ezmath.ai.data.pref.PrefConst
import com.amobilab.ezmath.ai.presentation.common.shared_components.CropCustomView
import com.amobilab.ezmath.ai.presentation.common.shared_values.ChatQuestionMode
import com.amobilab.ezmath.ai.presentation.common.shared_viewmodels.NavigatorViewModel


@AppPreview
@Composable
fun ScanEditViewPreview() {
    PreviewAssist.initVariables(LocalContext.current)
    ScanEditView(
        viewModel = ScanViewModel(),
        coinViewModel = CoinViewModel(),
        ChatQuestionMode.Math,
        "",
        true,
        RectF()
    ) { _, _ ->
    }
}

@Composable
fun ScanEditView(
    viewModel: ScanViewModel,
    coinViewModel: CoinViewModel,
    scanMode: ChatQuestionMode,
    urlBitmap: String,
    isFromCamera: Boolean,
    rectF: RectF,
    onEditCompleted: (urlImage: String, ask: String) -> Unit
) {

    val adjustedRectF = RectF(rectF).apply {
        left = left + 14
        right = right - 14
    }


    val context = LocalContext.current

    val navigatorViewModel = NavigatorViewModel.getInstance()


    var heightBottom by remember { mutableIntStateOf(0) }

    val inputContent = remember { mutableStateOf("") }

    viewModel.fetchOpenScan(context, scanMode, urlBitmap, adjustedRectF)

    val selectedLanguage =
        remember { mutableStateOf(PrefAssist.getString(PrefConst.TRANSLATE_TARGET)) }

    var cropRectF by remember { mutableStateOf(viewModel.rectFFromActivity.value) }


    var topSpacerHeight by remember { mutableStateOf(0) }
    var bottomSpacerHeight by remember { mutableStateOf(0) }
    var parentWidth by remember { mutableStateOf(0) }
    var parentHeight by remember { mutableStateOf(0) }

    val cropCustomView = remember { CropCustomView(context) }
    // Gọi setLimitMove sau khi các kích thước đã sẵn sàng
    LaunchedEffect(topSpacerHeight, bottomSpacerHeight, parentWidth, parentHeight) {
        if (topSpacerHeight > 0 && bottomSpacerHeight > 0 && parentWidth > 0 && parentHeight > 0) {
            val limitTop = topSpacerHeight
            val limitBottom = parentHeight - bottomSpacerHeight

            val paddingLimit = 12
            cropCustomView.setLimitMove(
                limitMoveLeft = 0 + paddingLimit,
                limitMoveRight = parentWidth - paddingLimit,
                limitMoveTop = limitTop + paddingLimit,
                limitMoveBottom = limitBottom - paddingLimit
            )
        }
    }

    var isNavigatingBack by remember { mutableStateOf(false) }

    Scaffold { innerPadding ->

        Box(modifier = Modifier.fillMaxSize()) {
            AndroidView(
//                modifier = Modifier.background(Color.Green),
                factory = { _ ->
                    val rectFFromActivity = viewModel.rectFFromActivity.value
                        ?: return@AndroidView cropCustomView
                    val imageBitmapFromActivity = viewModel.imageBitmapFromActivity.value
                        ?: return@AndroidView cropCustomView
                    // Creates view
                    cropCustomView.turnOnOffAll(true)
                    cropCustomView.setModeFrame(3)
                    cropCustomView.setFrameRect(rectFFromActivity)
                    cropCustomView.turnOnOffChangeSize(true)
                    cropCustomView.showIcon(false)
                    cropCustomView.turnOnOffMove(true)
                    cropCustomView.setBackgroundBitmap(imageBitmapFromActivity)
                    cropCustomView
                },
                update = { view ->
                    cropRectF = view.getRectOnScreen()
                }
            )

            AppColumn(modifier = Modifier
                .fillMaxSize()
                .onGloballyPositioned { coordinates ->
                    parentWidth = coordinates.size.width
                    parentHeight = coordinates.size.height
                }) {

                AppColumn(Modifier
                    .fillMaxWidth()
                    .onGloballyPositioned { coordinates ->
                        topSpacerHeight = coordinates.size.height
                    }) {
                    AppRowCentered(
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(Color.Black)
                            .height(AppSize.APPBAR_HEIGHT + innerPadding.calculateTopPadding())
                            .zIndex(200f)
                            .background(color = AppColors.current.actionBarColor)
                            .padding(top = innerPadding.calculateTopPadding()),
                    ) {
                        AppIcon(
                            imageVector = ImageVector.vectorResource(R.drawable.ic_back),
                            tint = Color.White,
                            size = AppSize.ICON_SIZE,
                            clickZone = AppSize.MIN_TOUCH_SIZE,
                        ) {
                            if (!isNavigatingBack) {
                                isNavigatingBack = true
                                navigatorViewModel.navigateBack()
                            }
                        }
                        AppSpacer(modifier = Modifier.weight(1f))
//                        AppText(
//                            text = stringResource(viewModel.modeScanFromActivity.value!!.titleId),
//                            modifier = Modifier
//                                .weight(1f),
//                            fontSize = AppFontSize.TITLE,
//                            fontWeight = FontWeight.Bold,
//                            textAlign = TextAlign.Start,
//                            color = Color.White
//                        )

                        AppRow(
                            verticalAlignment = Alignment.CenterVertically,
                        ) {
                            AppText(
                                modifier = Modifier.clickable {
                                    if (PrefAssist.getInt(PrefConst.FREE_CHAT) <= 0 &&
                                        PrefAssist.getLong(PrefConst.TOTAL_COIN_BALANCE) <= 0) {
                                        coinViewModel.showInsufficientCoinDialog()
                                        return@clickable
                                    }


                                    val croppedBitmap = cropCustomView.getCroppedBitmapFromCapture()
                                        ?: return@clickable
                                    viewModel.saveCropImageBitmap(croppedBitmap.asImageBitmap())
                                    val cropImageBitmap =
                                        viewModel.cropImageBitmap ?: return@clickable
                                    debugLog(" ${inputContent.value}")
                                    debugLog(" ${scanMode.id}")
                                    onEditCompleted(
                                        viewModel.saveImageToCache(context, cropImageBitmap),
                                        inputContent.value
                                            .trim()
                                            .ifEmpty {
                                                if (scanMode == ChatQuestionMode.Translate) {
                                                    context.getString(
                                                        scanMode.promptImageId,
                                                        selectedLanguage.value
                                                    )
                                                } else {
                                                    context.getString(scanMode.promptImageId)
                                                }
                                            }
                                    )
                                },
                                text = stringResource(R.string.txtid_done),
                                fontSize = AppFontSize.BODY1,
                                fontWeight = FontWeight.W400,
                                lineHeight = 24.sp,
                                color = Color.White
                            )
                            AppSpacer(4.dp)

                        }
                        AppSpacer(16.dp)
                    }
                }
                // View nằm ở giữa
                AppSpacer(Modifier.weight(1f))
                // View nằm ở dưới cùng
                AppColumn (
                    modifier = Modifier // Đặt ở dưới cùng của Box chính
                        .fillMaxWidth()
                        .onGloballyPositioned { coordinates ->
                            bottomSpacerHeight = coordinates.size.height
                        },
                    verticalArrangement = Arrangement.Center,
                    horizontalAlignment = Alignment.CenterHorizontally,
                ) {
                    AppText(
                        modifier = Modifier
                            .padding(8.dp)
                            .clip(RoundedCornerShape(12.dp))
                            .background(Color.Black.copy(alpha = 0.6f))
                            .padding(8.dp),
                        text = stringResource(R.string.crop_only_one_question_to_get_better_answer),
                        color = Color.White,
                        fontWeight = FontWeight.W400,
                        fontSize = AppFontSize.SMALL,
                    )
                    AppColumnCentered(
                        modifier = Modifier
                            .heightIn(min = 100.dp)
                            .fillMaxWidth()
                            .background(Color.Black)
                            .imePadding()
                            .padding(top = 20.dp, bottom = 38.dp)
                    ) {
                        Box(modifier = Modifier.clickable {
                            cropCustomView.rotateBackgroundBitmap(90f)
                        }) {
                            AppIcon(
                                R.drawable.ic_rotate_image,
                                size = 64.dp,
                                tint = Color.White,
                            )
                        }
                    }

                }
            }
        }
    }
}