package com.amobilab.ezmath.ai.presentation.admob

import amobi.module.common.advertisements.AdvertsConfig
import amobi.module.common.advertisements.banner_ad.AdvertsManagerBanner
import amobi.module.compose.extentions.PreviewAssist
import amobi.module.compose.foundation.AppColumn
import amobi.module.compose.foundation.AppDivider
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.compose.LifecycleEventEffect
import com.amobilab.ezmath.ai.data.pref.AdIds
import com.google.firebase.crashlytics.FirebaseCrashlytics


@Composable
fun AdmobBannerCompose(
    modifier: Modifier = Modifier,
    onAdShow: () -> Unit = {},
) {

    var isViewInitialized by remember { mutableStateOf(false) }

    var isAdShowed by remember { mutableStateOf(false) }

    var onResumeCounter by remember {
        mutableIntStateOf(-1)
    }

    if (PreviewAssist.IS_PREVIEW)
        return

    LifecycleEventEffect(Lifecycle.Event.ON_RESUME) {
        onResumeCounter++
    }

    AppColumn {
        if (isAdShowed)
            AppDivider()
        AndroidView(
            modifier = modifier
                .fillMaxWidth(),
            factory = { ctx ->
                LinearLayout(ctx)
                    .apply {
                        layoutParams = ViewGroup.LayoutParams(
                            ViewGroup.LayoutParams.MATCH_PARENT,
                            ViewGroup.LayoutParams.WRAP_CONTENT
                        )
                        gravity = Gravity.CENTER
                        visibility = View.GONE
                    }
            },
            update = { adContainer ->
                if (!isViewInitialized) {
                    val showAdvertsBanner = showAdvertsBanner@{
                        if (isAdShowed)
                            return@showAdvertsBanner

                        if (AdvertsConfig.instance.isHideAd) {
                            adContainer.visibility = View.GONE
                            return@showAdvertsBanner
                        }

                        onAdShow()

                        FirebaseCrashlytics.getInstance().log("log showAdvertsBanner")

                        if (AdvertsManagerBanner.isBannerAdvertsAvailable())
                            AdvertsManagerBanner.showAdverts(adContainer)

                        isAdShowed = true
                    }

                    if (AdvertsManagerBanner.isBannerAdvertsAvailable()) {
                        showAdvertsBanner()
                    } else {
                        AdvertsManagerBanner.requestBannerAds(
                            listAdsID = AdIds.bannerGeneral(),
                            onAdLoadedListener = {
                                showAdvertsBanner()
                                return@requestBannerAds Unit
                            },
                        )
                    }

                    isViewInitialized = true
                    return@AndroidView
                }

                if (!isAdShowed &&
                    onResumeCounter > 0 &&
                    AdvertsManagerBanner.isBannerAdvertsAvailable()
                ) {
                    AdvertsManagerBanner.showAdverts(adContainer)
                }
            },
            onRelease = { adContainer ->
                adContainer.removeAllViews()
                AdvertsManagerBanner.destroyBannerAds()
            }
        )
    }
}