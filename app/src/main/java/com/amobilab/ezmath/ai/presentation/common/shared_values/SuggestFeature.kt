package com.amobilab.ezmath.ai.presentation.common.shared_values

import com.amobilab.ezmath.ai.R
import com.amobilab.ezmath.ai.data.models.PromptSuggestion


enum class SuggestFeature(
    val id: String,
    val listSuggestions: List<PromptSuggestion>
) {
    // Write an essay
    ChooseATopic(
        id = "Choose a topic",
        listSuggestions = listOf(
            PromptSuggestion(R.string.describe_place, R.string.describe_a_place_that_makes_you_feel_at_peace),
            PromptSuggestion(R.string.favorite_memory, R.string.describe_your_favorite_childhood_memory),
            PromptSuggestion(R.string.inspiring_event, R.string.describe_an_event_that_changed_your_life)
        )
    ),
    TypeOfEssay(
        id = "Type of essay",
        listSuggestions = listOf(
            PromptSuggestion(R.string.argumentative, R.string.argumentative_essay_example),
            PromptSuggestion(R.string.narrative, R.string.narrative_essay_example),
            PromptSuggestion(R.string.expository, R.string.expository_essay_example)
        )
    ),
    WordCount(
        id = "Word count",
        listSuggestions = listOf(
            PromptSuggestion(R.string.word_300, R.string.write_essay_300_words),
            PromptSuggestion(R.string.word_500, R.string.write_essay_500_words),
            PromptSuggestion(R.string.word_1000, R.string.write_essay_1000_words)
        )
    ),
    LanguageTone(
        id = "Language + tone",
        listSuggestions = listOf(
            PromptSuggestion(R.string.formal, R.string.write_formally_about_topic),
            PromptSuggestion(R.string.academic, R.string.write_academically_about_topic),
            PromptSuggestion(R.string.informal, R.string.write_informally_like_a_blog_post)
        )
    ),

    // Literature
    TitleOfWork(
        id = "Title of the work",
        listSuggestions = listOf(
            PromptSuggestion(R.string.the_great_gatsby, R.string.write_about_the_great_gatsby),
            PromptSuggestion(R.string.to_kill_a_mockingbird, R.string.write_about_to_kill_a_mockingbird),
            PromptSuggestion(R.string.romeo_and_juliet, R.string.write_about_romeo_and_juliet)
        )
    ),
    Author(
        id = "Author",
        listSuggestions = listOf(
            PromptSuggestion(R.string.f_scott_fitzgerald, R.string.write_about_f_scott_fitzgerald),
            PromptSuggestion(R.string.harper_lee, R.string.write_about_harper_lee),
            PromptSuggestion(R.string.william_shakespeare, R.string.write_about_william_shakespeare)
        )
    ),
    AnalysisType(
        id = "What do you want to analyze?",
        listSuggestions = listOf(
            PromptSuggestion(R.string.character_analysis, R.string.do_character_analysis),
            PromptSuggestion(R.string.main_themes, R.string.analyze_main_themes),
            PromptSuggestion(R.string.symbolism, R.string.explain_the_symbolism_in_the_work)
        )
    ),
    LengthFormat(
        id = "Length / format",
        listSuggestions = listOf(
            PromptSuggestion(R.string.length_300, R.string.write_300_words_on_topic),
            PromptSuggestion(R.string.length_500, R.string.write_500_words_on_topic),
            PromptSuggestion(R.string.length_1000, R.string.write_1000_words_on_topic)
        )
    ),
    AcademicLevelEssay(
        id = "Academic level",
        listSuggestions = listOf(
            PromptSuggestion(R.string.middle_school, R.string.write_for_middle_school),
            PromptSuggestion(R.string.high_school, R.string.write_for_high_school),
            PromptSuggestion(R.string.university, R.string.write_for_university)
        )
    ),

    // Research and analysis
    ResearchTopic(
        id = "Research topic",
        listSuggestions = listOf(
            PromptSuggestion(R.string.climate_change, R.string.research_on_climate_change),
            PromptSuggestion(R.string.ai_on_jobs, R.string.research_on_ai_and_jobs),
            PromptSuggestion(R.string.social_media, R.string.research_on_social_media_effects)
        )
    ),
    ResearchGoal(
        id = "Research goal",
        listSuggestions = listOf(
            PromptSuggestion(R.string.info_gathering, R.string.goal_information_gathering),
            PromptSuggestion(R.string.trend_analysis, R.string.goal_trend_analysis),
            PromptSuggestion(R.string.problem_solution, R.string.goal_problem_and_solution)
        )
    ),
    PreferredSources(
        id = "Preferred sources",
        listSuggestions = listOf(
            PromptSuggestion(R.string.scientific_journals, R.string.use_scientific_journals),
            PromptSuggestion(R.string.official_articles, R.string.use_official_articles),
            PromptSuggestion(R.string.books, R.string.use_books_as_references)
        )
    ),
    AcademicLevelResearch(
        id = "Academic level (research)",
        listSuggestions = listOf(
            PromptSuggestion(R.string.high_school_students, R.string.research_for_high_school),
            PromptSuggestion(R.string.college_students, R.string.research_for_college_students),
            PromptSuggestion(R.string.advanced_research, R.string.research_for_advanced_level)
        )
    )
}
