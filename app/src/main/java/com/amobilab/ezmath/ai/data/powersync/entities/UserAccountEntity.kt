package com.amobilab.ezmath.ai.data.powersync.entities

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * PowerSync entity cho thông tin tài khoản người dùng
 * Đồng bộ hóa thông tin tài khoản giữa local SQLite và remote backend
 */
@Entity(tableName = "ps_user_accounts")
data class UserAccountEntity(
    @PrimaryKey
    val id: String, // PowerSync sử dụng String ID
    
    @ColumnInfo(name = "user_id")
    val userId: String,
    
    @ColumnInfo(name = "username")
    val username: String?,
    
    @ColumnInfo(name = "email")
    val email: String?,
    
    @ColumnInfo(name = "phone_number")
    val phoneNumber: String?,
    
    @ColumnInfo(name = "profile_picture_url")
    val profilePictureUrl: String?,
    
    @ColumnInfo(name = "authentication_provider")
    val authenticationProvider: String, // "google", "email", etc.
    
    @ColumnInfo(name = "is_active")
    val isActive: Boolean = true,
    
    @ColumnInfo(name = "subscription_type")
    val subscriptionType: String = "free", // "free", "premium", etc.
    
    @ColumnInfo(name = "subscription_expires_at")
    val subscriptionExpiresAt: Long? = null,
    
    @ColumnInfo(name = "created_at")
    val createdAt: Long = System.currentTimeMillis(),
    
    @ColumnInfo(name = "updated_at")
    val updatedAt: Long = System.currentTimeMillis(),
    
    // PowerSync metadata columns
    @ColumnInfo(name = "powersync_bucket")
    val powersyncBucket: String? = null,
    
    @ColumnInfo(name = "powersync_last_synced_at")
    val powersyncLastSyncedAt: Long? = null
)
