package com.amobilab.ezmath.ai.utils

import org.commonmark.Extension
import org.commonmark.ext.autolink.AutolinkExtension
import org.commonmark.ext.gfm.strikethrough.StrikethroughExtension
import org.commonmark.ext.gfm.tables.TablesExtension
import org.commonmark.ext.heading.anchor.HeadingAnchorExtension
import org.commonmark.ext.ins.InsExtension
import org.commonmark.ext.task.list.items.TaskListItemsExtension
import org.commonmark.node.AbstractVisitor
import org.commonmark.node.CustomNode
import org.commonmark.node.Delimited
import org.commonmark.node.Heading
import org.commonmark.node.Node
import org.commonmark.node.Nodes
import org.commonmark.node.SourceSpans
import org.commonmark.parser.IncludeSourceSpans
import org.commonmark.parser.Parser
import org.commonmark.parser.delimiter.DelimiterProcessor
import org.commonmark.parser.delimiter.DelimiterRun
import org.commonmark.renderer.NodeRenderer
import org.commonmark.renderer.html.HtmlNodeRendererContext
import org.commonmark.renderer.html.HtmlNodeRendererFactory
import org.commonmark.renderer.html.HtmlRenderer
import org.commonmark.renderer.html.HtmlWriter
import org.commonmark.renderer.markdown.MarkdownNodeRendererContext
import org.commonmark.renderer.markdown.MarkdownNodeRendererFactory
import org.commonmark.renderer.markdown.MarkdownRenderer
import org.commonmark.renderer.markdown.MarkdownWriter
import org.commonmark.renderer.text.TextContentNodeRendererContext
import org.commonmark.renderer.text.TextContentNodeRendererFactory
import org.commonmark.renderer.text.TextContentRenderer
import java.time.LocalDate
import java.time.LocalTime
import java.time.format.DateTimeFormatter

/**
 * Lớp xử lý Markdown chính, cung cấp các chức năng để parse, render và xử lý Markdown
 */
class MarkdownProcessor {
    // Các extension được sử dụng
    private val extensions: List<Extension> = listOf(
        TablesExtension.create(),
        AutolinkExtension.create(),
        HeadingAnchorExtension.create(),
        InsExtension.create(),
        StrikethroughExtension.create(),
        TaskListItemsExtension.create(),
        HighlightExtension.create()
    )

    // Parser và Renderer
    private val parser: Parser = Parser.builder()
        .extensions(extensions)
        .includeSourceSpans(IncludeSourceSpans.BLOCKS_AND_INLINES)
        .build()
    
    private val renderer: HtmlRenderer = HtmlRenderer.builder()
        .extensions(extensions)
        .build()

    /**
     * Parse văn bản Markdown thành cấu trúc dữ liệu AST
     * @param markdown Văn bản Markdown cần parse
     * @return Node gốc của cấu trúc AST
     */
    fun parseMarkdown(markdown: String): Node {
        return parser.parse(markdown)
    }

    /**
     * Render văn bản Markdown thành HTML
     * @param markdown Văn bản Markdown cần render
     * @return Chuỗi HTML
     */
    fun renderToHtml(markdown: String): String {
        val document = parseMarkdown(markdown)
        return renderer.render(document)
    }

    /**
     * Xử lý template trong văn bản Markdown
     * @param content Văn bản cần xử lý
     * @param dateFormat Định dạng ngày (mặc định: yyyy-MM-dd)
     * @param timeFormat Định dạng thời gian (mặc định: HH:mm)
     * @return Văn bản đã được xử lý
     */
    fun processTemplate(
        content: String,
        dateFormat: String = "yyyy-MM-dd",
        timeFormat: String = "HH:mm"
    ): String {
        val templateProcessor = TemplateProcessor(dateFormat, timeFormat)
        return templateProcessor.process(content)
    }

    /**
     * Tách phần YAML properties và nội dung chính
     * @param content Văn bản cần tách
     * @return Cặp (properties, content)
     */
    fun splitPropertiesAndContent(content: String): Pair<String, String> {
        val yamlBlockPattern = "\\A---\\s*\\n([\\s\\S]*?)\\n---".toRegex(RegexOption.MULTILINE)
        val matchResult = yamlBlockPattern.find(content)
        return if (matchResult != null) {
            val propertiesText = matchResult.groupValues[1]
            val contentText = content.substring(matchResult.range.last + 1)
            Pair(propertiesText, contentText)
        } else {
            Pair("", content)
        }
    }

    /**
     * Trích xuất cấu trúc đề mục từ văn bản Markdown
     * @param content Văn bản Markdown
     * @return Danh sách các đề mục
     */
    fun extractOutline(content: String): List<HeadingItem> {
        val document = parseMarkdown(content)
        val headings = mutableListOf<HeadingItem>()
        
        document.accept(object : AbstractVisitor() {
            override fun visit(heading: Heading) {
                val text = getHeadingText(heading)
                val level = heading.level
                val span = heading.sourceSpans.firstOrNull()
                val position = span?.inputIndex ?: 0
                
                headings.add(HeadingItem(text, level, position))
                visitChildren(heading)
            }
        })
        
        return headings
    }
    
    private fun getHeadingText(heading: Heading): String {
        val sb = StringBuilder()
        var child = heading.firstChild
        while (child != null) {
            if (child is org.commonmark.node.Text) {
                sb.append(child.literal)
            }
            child = child.next
        }
        return sb.toString()
    }

    /**
     * Chuyển đổi danh sách công việc trong Markdown
     * @param content Văn bản Markdown
     * @return Văn bản đã được xử lý
     */
    fun processTaskLists(content: String): String {
        return content
            .replace("- [ ]", "☐")
            .replace("- [x]", "☑")
    }
}

/**
 * Lớp xử lý template với các biến động như ngày và thời gian
 */
class TemplateProcessor(
    defaultDateFormat: String = "yyyy-MM-dd",
    defaultTimeFormat: String = "HH:mm"
) {
    private var defaultDateFormat: String =
        defaultDateFormat.takeIf { it.isNotBlank() } ?: "yyyy-MM-dd"
    private var defaultTimeFormat: String =
        defaultTimeFormat.takeIf { it.isNotBlank() } ?: "HH:mm"

    private val simpleDatePattern = "\\{\\{date\\}\\}"
    private val simpleTimePattern = "\\{\\{time\\}\\}"
    private val dateWithFormatPattern = "\\{\\{date:([^}]+)\\}\\}"
    private val timeWithFormatPattern = "\\{\\{time:([^}]+)\\}\\}"

    fun process(content: String): String {
        if (content.isBlank()) return content

        var result = content

        // Xử lý định dạng đơn giản
        result = result.replace(simpleDatePattern.toRegex()) {
            try {
                LocalDate.now().format(DateTimeFormatter.ofPattern(defaultDateFormat))
            } catch (_: Exception) {
                LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))
            }
        }

        result = result.replace(simpleTimePattern.toRegex()) {
            try {
                LocalTime.now().format(DateTimeFormatter.ofPattern(defaultTimeFormat))
            } catch (_: Exception) {
                LocalTime.now().format(DateTimeFormatter.ofPattern("HH:mm"))
            }
        }

        // Xử lý định dạng tùy chỉnh
        result = result.replace(dateWithFormatPattern.toRegex()) { matchResult ->
            val format = matchResult.groupValues[1]
            try {
                LocalDate.now().format(DateTimeFormatter.ofPattern(format))
            } catch (_: Exception) {
                LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))
            }
        }

        result = result.replace(timeWithFormatPattern.toRegex()) { matchResult ->
            val format = matchResult.groupValues[1]
            try {
                LocalTime.now().format(DateTimeFormatter.ofPattern(format))
            } catch (_: Exception) {
                LocalTime.now().format(DateTimeFormatter.ofPattern("HH:mm"))
            }
        }

        return result
    }
}

/**
 * Lớp đại diện cho một mục đề mục trong văn bản Markdown
 */
data class HeadingItem(
    val text: String,
    val level: Int,
    val position: Int
)

/**
 * Extension để hỗ trợ đánh dấu văn bản (highlight) với cú pháp ==text==
 */
class HighlightExtension : Parser.ParserExtension, HtmlRenderer.HtmlRendererExtension,
    TextContentRenderer.TextContentRendererExtension, MarkdownRenderer.MarkdownRendererExtension {

    companion object {
        @JvmStatic
        fun create(): Extension {
            return HighlightExtension()
        }
    }

    override fun extend(parserBuilder: Parser.Builder) {
        parserBuilder.customDelimiterProcessor(HighlightDelimiterProcessor())
    }

    override fun extend(rendererBuilder: HtmlRenderer.Builder) {
        rendererBuilder.nodeRendererFactory(HtmlNodeRendererFactory { context ->
            HighlightHtmlNodeRenderer(context)
        })
    }

    override fun extend(rendererBuilder: TextContentRenderer.Builder) {
        rendererBuilder.nodeRendererFactory(TextContentNodeRendererFactory { context ->
            HighlightTextContentNodeRenderer(context)
        })
    }

    override fun extend(rendererBuilder: MarkdownRenderer.Builder) {
        rendererBuilder.nodeRendererFactory(object : MarkdownNodeRendererFactory {
            override fun create(context: MarkdownNodeRendererContext): NodeRenderer {
                return HighlightMarkdownNodeRenderer(context)
            }

            override fun getSpecialCharacters(): Set<Char> {
                return setOf('=')
            }
        })
    }
}

/**
 * Lớp xử lý delimiter cho highlight
 */
class HighlightDelimiterProcessor : DelimiterProcessor {
    override fun getOpeningCharacter(): Char {
        return '='
    }

    override fun getClosingCharacter(): Char {
        return '='
    }

    override fun getMinLength(): Int {
        return 2
    }

    override fun process(
        openingRun: DelimiterRun,
        closingRun: DelimiterRun
    ): Int {
        if (openingRun.length() >= 2 && closingRun.length() >= 2) {
            val opener = openingRun.opener
            val highlight = Highlight()
            val sourceSpans = SourceSpans()
            sourceSpans.addAllFrom(openingRun.getOpeners(2))
            for (node in Nodes.between(opener, closingRun.closer)) {
                highlight.appendChild(node)
                sourceSpans.addAll(node.sourceSpans)
            }
            sourceSpans.addAllFrom(closingRun.getClosers(2))
            highlight.sourceSpans = sourceSpans.sourceSpans
            opener.insertAfter(highlight)
            return 2
        } else {
            return 0
        }
    }
}

/**
 * Node đại diện cho highlight
 */
class Highlight : CustomNode(), Delimited {
    private val delimiter: String = "=="

    override fun getOpeningDelimiter(): String {
        return delimiter
    }

    override fun getClosingDelimiter(): String {
        return delimiter
    }
}

/**
 * Renderer cơ sở cho highlight
 */
abstract class HighlightNodeRenderer : NodeRenderer {
    override fun getNodeTypes(): Set<Class<out Node?>?>? {
        return setOf(Highlight::class.java)
    }
}

/**
 * Renderer HTML cho highlight
 */
class HighlightHtmlNodeRenderer(
    private val context: HtmlNodeRendererContext,
    private val writer: HtmlWriter = context.writer
) : HighlightNodeRenderer() {

    override fun render(node: Node) {
        val attributes = context.extendAttributes(node, "mark", emptyMap())
        writer.tag("mark", attributes)
        var children = node.firstChild
        while (children != null) {
            val next = children.next
            context.render(children)
            children = next
        }
        writer.tag("/mark")
    }
}

/**
 * Renderer Markdown cho highlight
 */
class HighlightMarkdownNodeRenderer(
    private val context: MarkdownNodeRendererContext,
    private val writer: MarkdownWriter = context.writer
) : HighlightNodeRenderer() {
    override fun render(node: Node?) {
        val highlight = node as Highlight
        writer.raw(highlight.openingDelimiter)
        var children = node.firstChild
        while (children != null) {
            val next = children.next
            context.render(children)
            children = next
        }
        writer.raw(highlight.closingDelimiter)
    }
}

/**
 * Renderer text cho highlight
 */
class HighlightTextContentNodeRenderer(private val context: TextContentNodeRendererContext) :
    HighlightNodeRenderer() {
    override fun render(node: Node) {
        var children = node.firstChild
        while (children != null) {
            val next = children.next
            context.render(children)
            children = next
        }
    }
}

/**
 * Lớp kiểm tra lỗi Markdown
 */
class MarkdownLint {
    companion object {
        private val HEADING_REGEX = Regex("^(#{1,6})\\s.+$")
        private val HEADING_WITH_PUNCTUATION = Regex("^#{1,6}\\s.+[.,;:!?]$")
        private val INVALID_HEADING_SPACE = Regex("^#{1,6}\\s{2,}")
        private val TRAILING_SPACES = Regex("\\s{3,}$")
        private val CHINESE_LINK_REGEX = Regex("\\[[^\\[\\]]*]（[^（）]*）")
        private val CHINESE_EXCLAMATION_LINK = Regex("！\\[[^\\[\\]]*]")
    }

    data class Issue(val startIndex: Int, val endIndex: Int) {
        fun toPair() = Pair(startIndex, endIndex)
    }

    /**
     * Kiểm tra lỗi trong văn bản Markdown
     * @param markdown Văn bản Markdown cần kiểm tra
     * @return Danh sách các vị trí lỗi
     */
    fun validate(markdown: String): List<Pair<Int, Int>> {
        val issues = mutableListOf<Issue>()
        val codeRegions = detectCodeRegions(markdown)
        val nonCodeSegments = getNonCodeSegments(markdown, codeRegions)

        validateMarkdownElements(nonCodeSegments, issues)
        validateLineBasedElements(markdown, codeRegions, issues)

        return issues.map { it.toPair() }
    }

    private fun validateMarkdownElements(
        segments: List<Pair<Int, String>>,
        issues: MutableList<Issue>
    ) {
        segments.forEach { (offset, text) ->
            validateSegment(text, offset, issues)
        }
    }

    private fun validateSegment(text: String, offset: Int, issues: MutableList<Issue>) {
        var currentIndex = 0
        while (currentIndex < text.length) {
            currentIndex = validateLinks(text, currentIndex, offset, issues)
            currentIndex = validateChineseExclamation(text, currentIndex, offset, issues)
        }
    }

    private fun validateLinks(
        text: String,
        startIndex: Int,
        offset: Int,
        issues: MutableList<Issue>
    ): Int {
        val match = CHINESE_LINK_REGEX.find(text, startIndex) ?: return text.length
        issues.add(
            Issue(
                offset + match.range.first,
                offset + match.range.last + 1
            )
        )
        return match.range.last + 1
    }

    private fun validateChineseExclamation(
        text: String,
        startIndex: Int,
        offset: Int,
        issues: MutableList<Issue>
    ): Int {
        val match = CHINESE_EXCLAMATION_LINK.find(text, startIndex) ?: return text.length
        issues.add(
            Issue(
                offset + match.range.first,
                offset + match.range.first + 1
            )
        )
        return match.range.last + 1
    }

    private fun validateLineBasedElements(
        markdown: String,
        codeRegions: List<Pair<Int, Int>>,
        issues: MutableList<Issue>
    ) {
        val lines = markdown.split("\n")
        var currentIndex = 0
        var blankLineCount = 0
        var blankLineStart = -1

        lines.forEach { line ->
            if (!isInCodeRegion(currentIndex, codeRegions)) {
                when {
                    line.startsWith("#") -> validateHeading(line, currentIndex, issues)
                    TRAILING_SPACES.find(line) != null ->
                        issues.add(Issue(currentIndex, currentIndex + line.length))
                }

                // Kiểm tra dòng trống liên tiếp
                if (line.trim().isEmpty()) {
                    if (blankLineCount == 0) blankLineStart = currentIndex
                    blankLineCount++
                    if (blankLineCount > 3) {
                        issues.add(Issue(blankLineStart, currentIndex + line.length))
                    }
                } else {
                    blankLineCount = 0
                }
            }
            currentIndex += line.length + 1
        }
    }

    private fun validateHeading(line: String, lineStart: Int, issues: MutableList<Issue>) {
        // Kiểm tra định dạng tiêu đề
        val headingMatch = HEADING_REGEX.find(line)

        if (headingMatch == null) {
            issues.add(Issue(lineStart, lineStart + line.length))
            return
        }

        // Kiểm tra khoảng trắng giữa # và văn bản
        val invalidSpaceMatch = INVALID_HEADING_SPACE.find(line)
        if (invalidSpaceMatch != null) {
            val hashCount = line.takeWhile { it == '#' }.length
            issues.add(
                Issue(
                    lineStart + hashCount,
                    lineStart + invalidSpaceMatch.range.last + 1
                )
            )
            return
        }

        // Kiểm tra dấu câu ở cuối tiêu đề
        val punctuationMatch = HEADING_WITH_PUNCTUATION.find(line)
        if (punctuationMatch != null) {
            issues.add(
                Issue(
                    lineStart + line.length - 1,
                    lineStart + line.length
                )
            )
            return
        }
    }

    private fun detectCodeRegions(markdown: String): List<Pair<Int, Int>> {
        val codeRegions = mutableListOf<Pair<Int, Int>>()
        detectFencedCodeBlocks(markdown, codeRegions)
        detectInlineCodeBlocks(markdown, codeRegions)
        return codeRegions.sortedBy { it.first }
    }

    private fun detectFencedCodeBlocks(
        markdown: String,
        codeRegions: MutableList<Pair<Int, Int>>
    ) {
        val fencedCodeBlockPattern = "```[^`]*?\\n[\\s\\S]*?```".toRegex()
        val matches = fencedCodeBlockPattern.findAll(markdown)
        matches.forEach { match ->
            codeRegions.add(match.range.first to match.range.last + 1)
        }
    }

    private fun detectInlineCodeBlocks(
        markdown: String,
        codeRegions: MutableList<Pair<Int, Int>>
    ) {
        var i = 0
        while (i < markdown.length) {
            when (markdown[i]) {
                '`' -> {
                    if (!isEscaped(i, markdown)) {
                        var count = 1
                        while (i + count < markdown.length && markdown[i + count] == '`') {
                            count++
                        }

                        val endIndex = findClosingBackticks(markdown, i + count, count)
                        if (endIndex != -1) {
                            codeRegions.add(i to endIndex + count)
                            i = endIndex + count
                            continue
                        }
                    }
                }
                '\'' -> {
                    if (!isEscaped(i, markdown)) {
                        val endIndex = markdown.indexOf('\'', i + 1)
                        if (endIndex != -1 && !isEscaped(endIndex, markdown)) {
                            codeRegions.add(i to endIndex + 1)
                            i = endIndex + 1
                            continue
                        }
                    }
                }
            }
            i++
        }
    }

    private fun findClosingBackticks(
        markdown: String,
        startIndex: Int,
        count: Int
    ): Int {
        var i = startIndex
        while (i < markdown.length) {
            if (markdown[i] == '`' && !isEscaped(i, markdown)) {
                var matchCount = 1
                while (i + matchCount < markdown.length && 
                       markdown[i + matchCount] == '`' && 
                       matchCount < count
                ) {
                    matchCount++
                }
                if (matchCount == count) {
                    return i
                }
            }
            i++
        }
        return -1
    }

    private fun isEscaped(index: Int, text: String): Boolean {
        var count = 0
        var i = index - 1
        while (i >= 0 && text[i] == '\\') {
            count++
            i--
        }
        return count % 2 == 1
    }

    private fun isInCodeRegion(index: Int, codeRegions: List<Pair<Int, Int>>): Boolean {
        return codeRegions.any { (start, end) -> index in start until end }
    }

    private fun getNonCodeSegments(
        markdown: String,
        codeRegions: List<Pair<Int, Int>>
    ): List<Pair<Int, String>> {
        val segments = mutableListOf<Pair<Int, String>>()
        var currentIndex = 0

        codeRegions.forEach { (start, end) ->
            if (start > currentIndex) {
                segments.add(currentIndex to markdown.substring(currentIndex, start))
            }
            currentIndex = end
        }

        if (currentIndex < markdown.length) {
            segments.add(currentIndex to markdown.substring(currentIndex))
        }

        return segments
    }
}

/**
 * Hàm mở rộng để sử dụng MarkdownProcessor dễ dàng hơn
 */
fun String.renderMarkdown(): String {
    return MarkdownProcessor().renderToHtml(this)
}

fun String.processTemplate(
    dateFormat: String = "yyyy-MM-dd",
    timeFormat: String = "HH:mm"
): String {
    return MarkdownProcessor().processTemplate(this, dateFormat, timeFormat)
}

fun String.extractMarkdownOutline(): List<HeadingItem> {
    return MarkdownProcessor().extractOutline(this)
}

fun String.validateMarkdown(): List<Pair<Int, Int>> {
    return MarkdownLint().validate(this)
}
