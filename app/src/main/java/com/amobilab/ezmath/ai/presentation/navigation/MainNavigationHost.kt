package com.amobilab.ezmath.ai.presentation.navigation

import amobi.module.common.utils.debugLog
import android.graphics.RectF
import androidx.compose.animation.AnimatedContentTransitionScope
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.scaleIn
import androidx.compose.animation.scaleOut
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.toRoute
import com.amobilab.ezmath.ai.presentation.common.shared_viewmodels.MainDataViewModel
import com.amobilab.ezmath.ai.presentation.ui.authentication.AuthScreen
import com.amobilab.ezmath.ai.presentation.ui.calculator.CalculatorCompose
import com.amobilab.ezmath.ai.presentation.ui.chat_bot.ChatBotCompose
import com.amobilab.ezmath.ai.presentation.ui.coin_history.CoinTransactionHistoryScreen
import com.amobilab.ezmath.ai.presentation.ui.feature_screen.LiteratureScreen
import com.amobilab.ezmath.ai.presentation.ui.feature_screen.ResearchAndAnalysisScreen
import com.amobilab.ezmath.ai.presentation.ui.feature_screen.WriteAnEssayScreen
import com.amobilab.ezmath.ai.presentation.ui.home.HomeCompose
import com.amobilab.ezmath.ai.presentation.ui.iap.IapCompose
import com.amobilab.ezmath.ai.presentation.ui.onboarding.OnboardingCompose
import com.amobilab.ezmath.ai.presentation.ui.scan.ScanEditView
import com.amobilab.ezmath.ai.presentation.ui.scan.ScanViewModel
import com.amobilab.ezmath.ai.presentation.ui.screen_feedback.FeedbackSuggestionScreenCompose
import com.amobilab.ezmath.ai.presentation.ui.set_theme.SetThemeCompose
import com.amobilab.ezmath.ai.presentation.ui.testkey.ScreenTestKey
import com.amobilab.ezmath.ai.presentation.ui.user_info.UserDeleteScreen

@Composable
fun MainNavigationHost(
    modifier: Modifier,
    navController: NavHostController,
    startDestination: ScreenRoutes,
    innerPadding: PaddingValues
) {
    val mainDataViewModel = hiltViewModel<MainDataViewModel>()
    val scanViewModel = hiltViewModel<ScanViewModel>()

    NavHost(
        modifier = modifier,
        navController = navController,
        startDestination = startDestination,
        enterTransition = {
            slideIntoContainer(
                towards = AnimatedContentTransitionScope.SlideDirection.Left,
                animationSpec = tween(
                    durationMillis = 300,
                    easing = FastOutSlowInEasing
                )
            ) + fadeIn(
                animationSpec = tween(
                    durationMillis = 300,
                    easing = FastOutSlowInEasing
                )
            )
        },
        exitTransition = {
            slideOutOfContainer(
                towards = AnimatedContentTransitionScope.SlideDirection.Left,
                animationSpec = tween(
                    durationMillis = 300,
                    easing = FastOutSlowInEasing
                )
            ) + fadeOut(
                animationSpec = tween(
                    durationMillis = 300,
                    easing = FastOutSlowInEasing
                )
            ) + scaleOut(
                targetScale = 0.95f,
                animationSpec = tween(
                    durationMillis = 300,
                    easing = FastOutSlowInEasing
                )
            )
        },
        popEnterTransition = {
            slideIntoContainer(
                towards = AnimatedContentTransitionScope.SlideDirection.Right,
                animationSpec = tween(
                    durationMillis = 300,
                    easing = FastOutSlowInEasing
                )
            ) + fadeIn(
                animationSpec = tween(
                    durationMillis = 300,
                    easing = FastOutSlowInEasing
                )
            ) + scaleIn(
                initialScale = 0.95f,
                animationSpec = tween(
                    durationMillis = 300,
                    easing = FastOutSlowInEasing
                )
            )
        },
        popExitTransition = {
            slideOutOfContainer(
                towards = AnimatedContentTransitionScope.SlideDirection.Right,
                animationSpec = tween(
                    durationMillis = 300,
                    easing = FastOutSlowInEasing
                )
            ) + fadeOut(
                animationSpec = tween(
                    durationMillis = 300,
                    easing = FastOutSlowInEasing
                )
            )
        },
    ) {
        composable<ScreenRoutes.OnboardingScreen> {
            OnboardingCompose(
                onFinish = {
                    navController.navigate(ScreenRoutes.HomeScreen()) {
                        // Pop up to the start destination of the graph to
                        // avoid building up a large stack of destinations
                        popUpTo(navController.graph.startDestinationId) {
                            // Reset the back stack completely
                            inclusive = true
                        }
                    }

                }
            )
        }

        composable<ScreenRoutes.HomeScreen> {
            HomeCompose()
        }

        composable<ScreenRoutes.ScanDetail> { navBackStackEntry ->
            val parameters = navBackStackEntry.toRoute<ScreenRoutes.ScanDetail>()
            val rectFCustom = RectF(
                parameters.rectFLeft, parameters.rectFTop,
                parameters.rectFRight, parameters.rectFBottom
            )
            ScanEditView(
                viewModel = scanViewModel,
                coinViewModel = mainDataViewModel.coinViewModel,
                scanMode = parameters.scanMode,
                urlBitmap = parameters.url,
                isFromCamera = parameters.isFromCamera,
                rectF = rectFCustom,
            ) { urlImage, ask ->
                debugLog(" ${parameters.scanMode.id}")
                debugLog(" ask $ask")

                navController.navigate(
                    ScreenRoutes.ChatScreen(
                        urlBitmap = urlImage,
                        ask = ask,
                        mode = parameters.scanMode
                    )
                ) {
                    popUpTo(ScreenRoutes.ScanDetail::class.java.name) {
                        inclusive = true
                    }
                }
            }
        }
        composable<ScreenRoutes.Calculator> {
            CalculatorCompose()
        }

        composable<ScreenRoutes.InAppPurchaseRoute> {
            IapCompose()
        }

        composable<ScreenRoutes.CoinHistory> {
            CoinTransactionHistoryScreen(innerPadding) {
                navController.navigateUp()
            }
        }

        composable<ScreenRoutes.SignIn> {
            AuthScreen()
        }

        composable<ScreenRoutes.SetTheme> {
            SetThemeCompose() {
                navController.navigateUp()
            }
        }

        composable<ScreenRoutes.FeedbackSuggestion> {
            FeedbackSuggestionScreenCompose()
        }

        composable<ScreenRoutes.Literature> {
            LiteratureScreen(
                onSend = { _mode, prompt, imgUrl ->
                    navController.navigate(
                        ScreenRoutes.ChatScreen(
                            mode = _mode,
                            ask = prompt,
                            urlBitmap = imgUrl
                        )
                    ) {
                        popUpTo(navController.graph.startDestinationId)
                    }
                }
            )
        }

        composable<ScreenRoutes.ResearchAndAnalysis> {
            ResearchAndAnalysisScreen(
                onSend = { _mode, prompt, imgUrl ->
                    navController.navigate(
                        ScreenRoutes.ChatScreen(
                            mode = _mode,
                            ask = prompt,
                            urlBitmap = imgUrl
                        )
                    ) {
                        popUpTo(navController.graph.startDestinationId)
                    }
                }
            )
        }

        composable<ScreenRoutes.WriteAnEssay> {
            WriteAnEssayScreen(
                onSend = { _mode, prompt, imgUrl ->
                    navController.navigate(
                        ScreenRoutes.ChatScreen(
                            mode = _mode,
                            ask = prompt,
                            urlBitmap = imgUrl
                        )
                    ) {
                        popUpTo(navController.graph.startDestinationId)
                    }
                }
            )
        }

        composable<ScreenRoutes.UserDeleteScreen> { navBackStackEntry ->
            val parameters = navBackStackEntry.toRoute<ScreenRoutes.UserDeleteScreen>()
            val coinTotal = mainDataViewModel.coinViewModel.coinTotal.value

            UserDeleteScreen(
                parameters.userId,
                parameters.username,
                parameters.profilePictureUrl,
                parameters.phoneNumber,
                parameters.email,
                coin = coinTotal,
            )
        }

        composable<ScreenRoutes.ChatScreen> { navBackStackEntry ->
            val parameters = navBackStackEntry.toRoute<ScreenRoutes.ChatScreen>()
            debugLog(" ${parameters.mode.id}")
            ChatBotCompose(
                parameters.idHistory,
                parameters.ask,
                parameters.urlBitmap,
                parameters.mode
            )
        }

        composable<ScreenRoutes.ScreenTestKey> { navBackStackEntry ->
            ScreenTestKey()
        }
    }
}
