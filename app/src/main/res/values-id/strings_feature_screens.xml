<?xml version='1.0' encoding='utf-8'?>
<resources>
    <string name="generate_outline" translate_by="gpt"><PERSON>uat Garis Besar</string>
    <string name="placeholder_default" translate_by="gpt">Masukkan teks Anda di sini…</string>
    <string name="essay_screen_description" translate_by="gemini">Tentu! Saya bisa bantu. Mohon berikan detail untuk esai Anda di bawah ini.</string>
    <string name="label_choose_topic" translate_by="gpt">Pilih topik</string>
    <string name="label_essay_type" translate_by="gpt"><PERSON><PERSON> es<PERSON></string>
    <string name="label_word_count" translate_by="gpt">Jumlah kata</string>
    <string name="label_language_tone" translate_by="gpt">Bahasa + nada</string>
    <string name="placeholder_topic" translate_by="gpt">Deskripsikan tempat yang membuatmu merasa tenang</string>
    <string name="placeholder_essay_type" translate_by="gpt">Contoh: Argumentatif, Naratif…</string>
    <string name="placeholder_word_count" translate_by="gpt">Contoh: 300 kata, 500 kata, 1000 kata…</string>
    <string name="placeholder_language_tone" translate_by="gpt">Contoh: Formal, akademis, …</string>
    <string name="research_screen_description" translate_by="gpt">Tempat untuk mengubah data mentah menjadi cerita visual yang bermakna melalui penelitian dan analisis.</string>
    <string name="label_research_topic" translate_by="gpt">Topik penelitian</string>
    <string name="label_research_goal" translate_by="gpt">Tujuan penelitian</string>
    <string name="label_preferred_sources" translate_by="gpt">Sumber yang Diutamakan</string>
    <string name="label_depth_length" translate_by="gpt">Kedalaman / Panjang</string>
    <string name="label_academic_level" translate_by="gemini">Jenjang pendidikan</string>
    <string name="placeholder_research_topic" translate_by="gpt">Contoh: Perubahan iklim, Dampak AI terhadap Pekerjaan, …</string>
    <string name="placeholder_research_goal" translate_by="gpt">Pengumpulan Informasi, Analisis Tren…</string>
    <string name="placeholder_preferred_sources" translate_by="gpt">Jurnal ilmiah, buku, artikel resmi</string>
    <string name="placeholder_depth_length" translate_by="gpt">Contoh: 300 kata, 500 kata, 1000 kata…</string>
    <string name="placeholder_academic_level" translate_by="gpt">Contoh: Siswa sekolah menengah, mahasiswa, penelitian lanjutan, …</string>
    <string name="literature_screen_description" translate_by="gpt">Dari kata-kata hingga makna tersembunyi, kami membantu Anda mengungkap nilai sejati dari setiap karya sastra.</string>
    <string name="label_title_of_work" translate_by="gpt">Judul karya</string>
    <string name="label_author" translate_by="gpt">Penulis</string>
    <string name="label_analysis_type" translate_by="gpt">Apa yang ingin Anda analisis?</string>
    <string name="label_format" translate_by="gpt">Panjang / format</string>
    <string name="placeholder_title" translate_by="gpt">Contoh: The Great Gatsby</string>
    <string name="placeholder_author" translate_by="google">Mis: F. Scott Fitzgerald</string>
    <string name="placeholder_analysis_type" translate_by="gpt">Analisis karakter, Tema utama…</string>
    <string name="placeholder_format" translate_by="gpt">Contoh: 300 kata, 500 kata, 1000 kata…</string>
    <string name="placeholder_academic_level_literature" translate_by="gemini">SMP, SMA, atau universitas, …</string>
    <string name="research_outline_topic_label" translate_by="gemini">📘 Topik Riset: %1$s</string>
    <string name="research_outline_goal_label" translate_by="gemini">🎯 Tujuan Riset: %1$s</string>
    <string name="research_outline_sources_label" translate_by="gemini">📚 Sumber Pilihan: %1$s</string>
    <string name="research_outline_depth_label" translate_by="gpt">📏 Kedalaman/Panjang: %1$s</string>
    <string name="research_outline_academic_level_label" translate_by="gemini">🎓 Jenjang Akademik: %1$s</string>
    <string name="research_outline_suggested_outline_label" translate_by="gpt">🧾 Garis Besar yang Disarankan:</string>
    <string name="research_outline_introduction_title" translate_by="gpt">1. Pendahuluan</string>
    <string name="research_outline_introduction_overview" translate_by="gpt">- Gambaran singkat tentang %1$s</string>
    <string name="research_outline_introduction_importance" translate_by="gpt">- Pentingnya penelitian di tingkat %1$s</string>
    <string name="research_outline_objectives_title" translate_by="gpt">2. Tujuan</string>
    <string name="research_outline_objectives_goal" translate_by="gemini">- Perjelas tujuan utama: %1$s</string>
    <string name="research_outline_methodology_title" translate_by="gpt">3. Metodologi</string>
    <string name="research_outline_methodology_approach" translate_by="gpt">- Pendekatan penelitian</string>
    <string name="research_outline_methodology_sources" translate_by="gpt">- Sumber data: %1$s</string>
    <string name="research_outline_key_insights_title" translate_by="gpt">4. Wawasan Kunci</string>
    <string name="research_outline_key_insights_trends" translate_by="gpt">- Diskusikan tren, fakta, atau temuan analisis</string>
    <string name="research_outline_key_insights_citations" translate_by="gpt">- Gunakan kutipan jika perlu</string>
    <string name="research_outline_conclusion_title" translate_by="gpt">5. Kesimpulan</string>
    <string name="research_outline_conclusion_summary" translate_by="gpt">- Ringkasan temuan</string>
    <string name="research_outline_conclusion_implications" translate_by="gpt">- Implikasi atau pekerjaan di masa depan</string>
    <string name="essay_outline_topic_label" translate_by="gpt">✏️ Topik Esai: %1$s</string>
    <string name="essay_outline_type_label" translate_by="gpt">📝 Jenis Esai: %1$s</string>
    <string name="essay_outline_word_count_label" translate_by="gpt">🔢 Jumlah Kata: %1$s</string>
    <string name="essay_outline_language_tone_label" translate_by="gpt">🗣️ Bahasa &amp; Nada: %1$s</string>
    <string name="essay_outline_suggested_outline_label" translate_by="gpt">🧾 Garis Besar yang Disarankan:</string>
    <string name="essay_outline_introduction_title" translate_by="gpt">1. Pendahuluan</string>
    <string name="essay_outline_introduction_topic" translate_by="gpt">- Perkenalkan topik: %1$s</string>
    <string name="essay_outline_introduction_background" translate_by="gpt">- Berikan latar belakang/konteks</string>
    <string name="essay_outline_introduction_thesis" translate_by="gpt">- Nyatakan tesis</string>
    <string name="essay_outline_body_title" translate_by="gpt">2. Paragraf Tubuh</string>
    <string name="essay_outline_body_paragraph1" translate_by="gpt">- Paragraf 1: Argumen atau poin pertama</string>
    <string name="essay_outline_body_paragraph2" translate_by="gpt">- Paragraf 2: Bukti atau narasi pendukung</string>
    <string name="essay_outline_body_paragraph3" translate_by="gemini">Paragraf 3: Kontra-argumen atau detail tambahan</string>
    <string name="essay_outline_conclusion_title" translate_by="gpt">3. Kesimpulan</string>
    <string name="essay_outline_conclusion_summary" translate_by="gpt">- Ringkaskan poin-poin kunci</string>
    <string name="essay_outline_conclusion_restate" translate_by="gemini">Nyatakan kembali tesis dengan cara baru</string>
    <string name="essay_outline_conclusion_final" translate_by="gemini">Akhiri dengan pemikiran akhir yang kuat</string>
    <string name="essay_outline_notes_title" translate_by="gpt">✨ Catatan:</string>
    <string name="essay_outline_notes_tone" translate_by="gpt">- Pertahankan nada %1$s sepanjang waktu</string>
    <string name="essay_outline_notes_wordcount" translate_by="gpt">- Targetkan sekitar %1$s total</string>
    <string name="essay_outline_notes_structure" translate_by="gpt">- Ikuti struktur esai %1$s yang khas</string>
    <string name="literature_outline_title_label" translate_by="gpt">Judul: %1$s</string>
    <string name="literature_outline_author_label" translate_by="gpt">Penulis: %1$s</string>
    <string name="literature_outline_focus_label" translate_by="gpt">Fokus: %1$s</string>
    <string name="literature_outline_length_label" translate_by="gpt">Panjang: %1$s</string>
    <string name="literature_outline_academic_level_label" translate_by="gemini">Jenjang Akademik: %1$s</string>
    <string name="literature_outline_outline_label" translate_by="gpt">Garis Besar:</string>
    <string name="literature_outline_introduction_title" translate_by="google">1. Pendahuluan</string>
    <string name="literature_outline_introduction_context" translate_by="gpt">Perkenalkan karya sastra dan konteksnya.</string>
    <string name="literature_outline_introduction_author" translate_by="gemini">Sebutkan penulis dan relevansi dengan fokus analisis yang dipilih.</string>
    <string name="literature_outline_background_title" translate_by="gpt">2. Latar Belakang</string>
    <string name="literature_outline_background_summary" translate_by="gemini">Ringkasan alur atau karakter utama (tergantung jenis analisis).</string>
    <string name="literature_outline_background_context" translate_by="gemini">Berikan konteks yang diperlukan untuk analisis lebih mendalam.</string>
    <string name="literature_outline_analysis_title" translate_by="gpt">3. Analisis Utama</string>
    <string name="literature_outline_analysis_deep_dive" translate_by="gemini">Selami lebih dalam: %1$s.</string>
    <string name="literature_outline_analysis_evidence" translate_by="gpt">Gunakan bukti dari teks: kutipan, peristiwa, simbolisme, dll.</string>
    <string name="literature_outline_connections_title" translate_by="gpt">4. Koneksi</string>
    <string name="literature_outline_connections_themes" translate_by="gpt">Analisis tautan ke tema yang lebih besar atau implikasi dunia nyata.</string>
    <string name="literature_outline_connections_contrast" translate_by="gpt">Secara opsional, bandingkan dengan karakter atau karya lainnya.</string>
    <string name="literature_outline_conclusion_title" translate_by="gpt">5. Kesimpulan</string>
    <string name="literature_outline_conclusion_insights" translate_by="google">Nyatakan kembali wawasan kunci.</string>
    <string name="literature_outline_conclusion_value" translate_by="gemini">Refleksikan nilai pekerjaan dari perspektif akademis.</string>
</resources>