<?xml version='1.0' encoding='utf-8'?>
<resources>
    <string name="generate_outline" translate_by="gpt">Gliederung erstellen</string>
    <string name="placeholder_default" translate_by="gemini">Gib hier deinen Text ein…</string>
    <string name="essay_screen_description" translate_by="gemini">Klar! Ich helfe gerne dabei. Bitte gib unten Details zu deinem Essay an.</string>
    <string name="label_choose_topic" translate_by="gpt">Wähle ein Thema</string>
    <string name="label_essay_type" translate_by="gpt">Art des Aufsatzes</string>
    <string name="label_word_count" translate_by="gpt">Wort<PERSON>hl</string>
    <string name="label_language_tone" translate_by="gpt">Sprache + Ton</string>
    <string name="placeholder_topic" translate_by="gemini"><PERSON>schre<PERSON> einen Ort, an dem du dich friedlich fühlst</string>
    <string name="placeholder_essay_type" translate_by="gemini">Argumentativ, Narrativ…</string>
    <string name="placeholder_word_count" translate_by="gemini">Z. B. 300 Wörter, 500 Wörter, 1000 Wörter…</string>
    <string name="placeholder_language_tone" translate_by="gpt">Beispiel: Formal, akademisch, …</string>
    <string name="research_screen_description" translate_by="gpt">Ein Ort, um Rohdaten durch Forschung und Analyse in bedeutungsvolle, visuelle Geschichten zu verwandeln.</string>
    <string name="label_research_topic" translate_by="gpt">Forschungsthema</string>
    <string name="label_research_goal" translate_by="gpt">Forschungsziel</string>
    <string name="label_preferred_sources" translate_by="gpt">Bevorzugte Quellen</string>
    <string name="label_depth_length" translate_by="gpt">Tiefe / Länge</string>
    <string name="label_academic_level" translate_by="gpt">Akademisches Niveau</string>
    <string name="placeholder_research_topic" translate_by="gpt">Beispiel: Klimawandel, Die Auswirkungen von KI auf Arbeitsplätze, …</string>
    <string name="placeholder_research_goal" translate_by="gemini">Informationsbeschaffung, Trendanalyse…</string>
    <string name="placeholder_preferred_sources" translate_by="gpt">Wissenschaftliche Zeitschriften, Bücher, offizielle Artikel</string>
    <string name="placeholder_depth_length" translate_by="gemini">Z. B. 300 Wörter, 500 Wörter, 1000 Wörter…</string>
    <string name="placeholder_academic_level" translate_by="gemini">Oberstufenschüler, Studenten, fortgeschrittene Forschung, …</string>
    <string name="literature_screen_description" translate_by="gemini">Von Worten zu verborgenen Bedeutungen – wir helfen dir, den wahren Wert jedes literarischen Werks zu entdecken.</string>
    <string name="label_title_of_work" translate_by="gpt">Titel des Werkes</string>
    <string name="label_author" translate_by="gpt">Autor</string>
    <string name="label_analysis_type" translate_by="gpt">Was möchten Sie analysieren?</string>
    <string name="label_format" translate_by="gpt">Länge / Format</string>
    <string name="placeholder_title" translate_by="gpt">Der große Gatsby</string>
    <string name="placeholder_author" translate_by="google">Beispiel: F. Scott Fitzgerald</string>
    <string name="placeholder_analysis_type" translate_by="google">Beispiel: Charakteranalyse, Hauptthemen …</string>
    <string name="placeholder_format" translate_by="gemini">Z. B. 300 Wörter, 500 Wörter, 1000 Wörter…</string>
    <string name="placeholder_academic_level_literature" translate_by="gemini">Mittelschule, Gymnasium oder Universität, …</string>
    <string name="research_outline_topic_label" translate_by="gpt">📘 Forschungsthema: %1$s</string>
    <string name="research_outline_goal_label" translate_by="gpt">🎯 Forschungsziel: %1$s</string>
    <string name="research_outline_sources_label" translate_by="gpt">📚 Bevorzugte Quellen: %1$s</string>
    <string name="research_outline_depth_label" translate_by="gpt">📏 Tiefe/Länge: %1$s</string>
    <string name="research_outline_academic_level_label" translate_by="gpt">🎓 Akademisches Niveau: %1$s</string>
    <string name="research_outline_suggested_outline_label" translate_by="gpt">🧾 Vorgeschlagene Gliederung:</string>
    <string name="research_outline_introduction_title" translate_by="gpt">1. Einführung</string>
    <string name="research_outline_introduction_overview" translate_by="gemini">Kurzer Überblick über %1$s</string>
    <string name="research_outline_introduction_importance" translate_by="gpt">- Bedeutung der Forschung auf der %1$s-Ebene</string>
    <string name="research_outline_objectives_title" translate_by="gpt">2. Ziele</string>
    <string name="research_outline_objectives_goal" translate_by="gpt">- Kläre das Hauptziel: %1$s</string>
    <string name="research_outline_methodology_title" translate_by="gpt">3. Methodik</string>
    <string name="research_outline_methodology_approach" translate_by="gpt">- Forschungsansatz</string>
    <string name="research_outline_methodology_sources" translate_by="gpt">- Datenquellen: %1$s</string>
    <string name="research_outline_key_insights_title" translate_by="gpt">4. Wichtige Erkenntnisse</string>
    <string name="research_outline_key_insights_trends" translate_by="gpt">- Diskutieren Sie Trends, Fakten oder Analyseergebnisse</string>
    <string name="research_outline_key_insights_citations" translate_by="gemini">Zitiere bei Bedarf</string>
    <string name="research_outline_conclusion_title" translate_by="gpt">5. Fazit</string>
    <string name="research_outline_conclusion_summary" translate_by="gpt">- Zusammenfassung der Ergebnisse</string>
    <string name="research_outline_conclusion_implications" translate_by="google">- Implikationen oder zukünftige Arbeit</string>
    <string name="essay_outline_topic_label" translate_by="gpt">✏️ Aufsatzthema: %1$s</string>
    <string name="essay_outline_type_label" translate_by="gpt">📝 Aufsatztyp: %1$s</string>
    <string name="essay_outline_word_count_label" translate_by="gpt">🔢 Wortanzahl: %1$s</string>
    <string name="essay_outline_language_tone_label" translate_by="gpt">🗣️ Sprache &amp; Ton: %1$s</string>
    <string name="essay_outline_suggested_outline_label" translate_by="gpt">🧾 Vorgeschlagene Gliederung:</string>
    <string name="essay_outline_introduction_title" translate_by="gpt">1. Einführung</string>
    <string name="essay_outline_introduction_topic" translate_by="gemini">Thema vorstellen: %1$s</string>
    <string name="essay_outline_introduction_background" translate_by="gemini">Hintergrund/Kontext bereitstellen</string>
    <string name="essay_outline_introduction_thesis" translate_by="gpt">- Formuliere die These</string>
    <string name="essay_outline_body_title" translate_by="gpt">2. Hauptabsätze</string>
    <string name="essay_outline_body_paragraph1" translate_by="gpt">- Absatz 1: Erstes Argument oder Punkt</string>
    <string name="essay_outline_body_paragraph2" translate_by="gpt">- Absatz 2: Unterstützende Beweise oder Erzählung</string>
    <string name="essay_outline_body_paragraph3" translate_by="gpt">- Absatz 3: Gegenargument oder zusätzliche Details</string>
    <string name="essay_outline_conclusion_title" translate_by="gpt">3. Fazit</string>
    <string name="essay_outline_conclusion_summary" translate_by="gpt">- Fassen Sie die wichtigsten Punkte zusammen</string>
    <string name="essay_outline_conclusion_restate" translate_by="gpt">- Formuliere die These anders.</string>
    <string name="essay_outline_conclusion_final" translate_by="google">- Schließen Sie mit einem starken letzten Gedanken ab</string>
    <string name="essay_outline_notes_title" translate_by="gpt">✨ Notizen:</string>
    <string name="essay_outline_notes_tone" translate_by="gpt">- Halte einen %1$s Ton durchgehend bei</string>
    <string name="essay_outline_notes_wordcount" translate_by="gemini">Ziel sind insgesamt etwa %1$s.</string>
    <string name="essay_outline_notes_structure" translate_by="gpt">- Befolge die typische %1$s Essay-Struktur</string>
    <string name="literature_outline_title_label" translate_by="gpt">Titel: %1$s</string>
    <string name="literature_outline_author_label" translate_by="gpt">Autor: %1$s</string>
    <string name="literature_outline_focus_label" translate_by="gpt">Fokus: %1$s</string>
    <string name="literature_outline_length_label" translate_by="gpt">Länge: %1$s</string>
    <string name="literature_outline_academic_level_label" translate_by="gpt">Akademisches Niveau: %1$s</string>
    <string name="literature_outline_outline_label" translate_by="gemini">Gliederung:</string>
    <string name="literature_outline_introduction_title" translate_by="gpt">1. Einführung</string>
    <string name="literature_outline_introduction_context" translate_by="gpt">Stellen Sie das literarische Werk und seinen Kontext vor.</string>
    <string name="literature_outline_introduction_author" translate_by="google">Erwähnen Sie den Autor und die Relevanz für den gewählten Analysefokus.</string>
    <string name="literature_outline_background_title" translate_by="gpt">2. Hintergrund</string>
    <string name="literature_outline_background_summary" translate_by="gemini">Zusammenfassung der Handlung oder der wichtigsten Charaktere (je nach Analysetyp).</string>
    <string name="literature_outline_background_context" translate_by="google">Bieten Sie den notwendigen Kontext für eine tiefere Analyse.</string>
    <string name="literature_outline_analysis_title" translate_by="gpt">3. Hauptanalyse</string>
    <string name="literature_outline_analysis_deep_dive" translate_by="gpt">Tauchen Sie ein in: %1$s.</string>
    <string name="literature_outline_analysis_evidence" translate_by="gpt">Verwenden Sie Beweise aus dem Text: Zitate, Ereignisse, Symbolik usw.</string>
    <string name="literature_outline_connections_title" translate_by="gpt">4. Verbindungen</string>
    <string name="literature_outline_connections_themes" translate_by="gpt">Linkanalyse zu größeren Themen oder realen Auswirkungen.</string>
    <string name="literature_outline_connections_contrast" translate_by="gemini">Optional mit anderen Charakteren oder Werken kontrastieren.</string>
    <string name="literature_outline_conclusion_title" translate_by="gpt">5. Fazit</string>
    <string name="literature_outline_conclusion_insights" translate_by="gpt">Wesentliche Erkenntnisse zusammenfassen.</string>
    <string name="literature_outline_conclusion_value" translate_by="gpt">Reflektiere über den Wert der Arbeit aus einer akademischen Perspektive.</string>
</resources>