<?xml version='1.0' encoding='utf-8'?>
<resources>
    <string name="generate_outline" translate_by="gemini">Გეგმის გენერირება</string>
    <string name="placeholder_default" translate_by="google">ექს: შეიყვანეთ თქვენი ტექსტი აქ …</string>
    <string name="essay_screen_description" translate_by="gemini">Რა თქმა უნდა! შემიძლია დაგეხმაროთ. გთხოვთ, ქვემოთ მოგვაწოდოთ თქვენი ესეს დეტალები.</string>
    <string name="label_choose_topic" translate_by="gpt">Აირჩიეთ თემა</string>
    <string name="label_essay_type" translate_by="gpt">Ესეების ტიპი</string>
    <string name="label_word_count" translate_by="gpt">Სიტყვების რაოდენობა</string>
    <string name="label_language_tone" translate_by="gpt">Ენა + ტონი</string>
    <string name="placeholder_topic" translate_by="gemini">Აღწერეთ ადგილი, სადაც სიმშვიდეს გრძნობთ</string>
    <string name="placeholder_essay_type" translate_by="gemini">Არგუმენტული, თხრობითი…</string>
    <string name="placeholder_word_count" translate_by="google">ექს: 300 სიტყვა, 500 სიტყვა, 1000 სიტყვა …</string>
    <string name="placeholder_language_tone" translate_by="google">ექს: ფორმალური, აკადემიური, …</string>
    <string name="research_screen_description" translate_by="google">ნედლეული მონაცემების მნიშვნელოვან, ვიზუალურ ისტორიებად გადაქცევის ადგილი კვლევისა და ანალიზის საშუალებით.</string>
    <string name="label_research_topic" translate_by="gpt">Კვლევის თემა</string>
    <string name="label_research_goal" translate_by="gpt">Კვლევის მიზანი</string>
    <string name="label_preferred_sources" translate_by="google">სასურველი წყაროები</string>
    <string name="label_depth_length" translate_by="gpt">Სიღრმე / სიგრძე</string>
    <string name="label_academic_level" translate_by="gpt">Აკადემიური დონე</string>
    <string name="placeholder_research_topic" translate_by="gpt">Კლიმატის ცვლილება, ხელოვნური ინტელექტის გავლენა სამუშაოებზე, …</string>
    <string name="placeholder_research_goal" translate_by="gpt">Მაგალითად: ინფორმაცია შეგროვება, ტენდენციების ანალიზი…</string>
    <string name="placeholder_preferred_sources" translate_by="google">ექს: სამეცნიერო ჟურნალები, წიგნები, ოფიციალური სტატიები</string>
    <string name="placeholder_depth_length" translate_by="google">ექს: 300 სიტყვა, 500 სიტყვა, 1000 სიტყვა …</string>
    <string name="placeholder_academic_level" translate_by="gpt">Მაღალი სკოლის სტუდენტები, კოლეჯის სტუდენტები, მოწინავე კვლევა, …</string>
    <string name="literature_screen_description" translate_by="gpt">Სიტყვებიდან დამალულ მნიშვნელობებამდე, ჩვენ გეხმარებით გაარკვიოთ თითოეული ლიტერატურული ნაწარმოების ჭეშმარიტი ღირებულება.</string>
    <string name="label_title_of_work" translate_by="gemini">Ნაწარმოების სათაური</string>
    <string name="label_author" translate_by="gpt">Ავტორი</string>
    <string name="label_analysis_type" translate_by="google">რისი გაანალიზება გსურთ?</string>
    <string name="label_format" translate_by="gpt">Სიგრძე / ფორმატი</string>
    <string name="placeholder_title" translate_by="gemini">Დიდი გეტსბი</string>
    <string name="placeholder_author" translate_by="gpt">Ფ. სკოტ ფიცჯერალდი</string>
    <string name="placeholder_analysis_type" translate_by="google">ექს: პერსონაჟების ანალიზი, ძირითადი თემები …</string>
    <string name="placeholder_format" translate_by="google">ექს: 300 სიტყვა, 500 სიტყვა, 1000 სიტყვა …</string>
    <string name="placeholder_academic_level_literature" translate_by="gpt">Მაგალითად: საშუალო სკოლა, მაღალი სკოლა ან უნივერსიტეტი, …</string>
    <string name="research_outline_topic_label" translate_by="gpt">📘 კვლევის თემა: %1$s</string>
    <string name="research_outline_goal_label" translate_by="gpt">🎯 კვლევის მიზანი: %1$s</string>
    <string name="research_outline_sources_label" translate_by="gpt">📚 სასურველი წყაროები: %1$s</string>
    <string name="research_outline_depth_label" translate_by="gpt">📏 სიღრმე/სიგრძე: %1$s</string>
    <string name="research_outline_academic_level_label" translate_by="gpt">🎓 აკადემიური დონე: %1$s</string>
    <string name="research_outline_suggested_outline_label" translate_by="gemini">🧾 შემოთავაზებული მონახაზი:</string>
    <string name="research_outline_introduction_title" translate_by="gpt">1. შესავალი</string>
    <string name="research_outline_introduction_overview" translate_by="gpt">- მოკლე მიმოხილვა %1$s-ის</string>
    <string name="research_outline_introduction_importance" translate_by="gpt">- კვლევის მნიშვნელობა %1$s დონეზე</string>
    <string name="research_outline_objectives_title" translate_by="gpt">2. მიზნები</string>
    <string name="research_outline_objectives_goal" translate_by="gpt">- გაარკვიეთ ძირითადი მიზანი: %1$s</string>
    <string name="research_outline_methodology_title" translate_by="gpt">3. მეთოდოლოგია</string>
    <string name="research_outline_methodology_approach" translate_by="gpt">- კვლევის მიდგომა</string>
    <string name="research_outline_methodology_sources" translate_by="gpt">- მონაცემთა წყაროები: %1$s</string>
    <string name="research_outline_key_insights_title" translate_by="gemini">4. ძირითადი მიგნებები</string>
    <string name="research_outline_key_insights_trends" translate_by="gpt">- განიხილეთ ტენდენციები, ფაქტები ან ანალიზის შედეგები</string>
    <string name="research_outline_key_insights_citations" translate_by="gpt">- საჭიროების შემთხვევაში გამოიყენეთ ციტატები</string>
    <string name="research_outline_conclusion_title" translate_by="gpt">5. დასკვნა</string>
    <string name="research_outline_conclusion_summary" translate_by="gpt">- კვლევის შედეგების შეჯამება</string>
    <string name="research_outline_conclusion_implications" translate_by="gemini">Შედეგები ან სამომავლო სამუშაო</string>
    <string name="essay_outline_topic_label" translate_by="gpt">✏️ ესე თემა: %1$s</string>
    <string name="essay_outline_type_label" translate_by="gpt">📝 ესე ტიპი: %1$s</string>
    <string name="essay_outline_word_count_label" translate_by="gpt">🔢 სიტყვების რაოდენობა: %1$s</string>
    <string name="essay_outline_language_tone_label" translate_by="gpt">🗣️ ენა და ტონი: %1$s</string>
    <string name="essay_outline_suggested_outline_label" translate_by="gemini">🧾 შემოთავაზებული მონახაზი:</string>
    <string name="essay_outline_introduction_title" translate_by="gpt">1. შესავალი</string>
    <string name="essay_outline_introduction_topic" translate_by="gpt">- თემის წარდგენა: %1$s</string>
    <string name="essay_outline_introduction_background" translate_by="gpt">- მიაწვდეთ ფონური/კონტექსტი</string>
    <string name="essay_outline_introduction_thesis" translate_by="gpt">- დაასახელეთ თეზისი</string>
    <string name="essay_outline_body_title" translate_by="gpt">2. სხეულის აბზაცები</string>
    <string name="essay_outline_body_paragraph1" translate_by="gpt">- აბზაცი 1: პირველი არგუმენტი ან წერტილი</string>
    <string name="essay_outline_body_paragraph2" translate_by="gemini">Პარაგრაფი 2: დამხმარე მტკიცებულება ან თხრობა</string>
    <string name="essay_outline_body_paragraph3" translate_by="gemini">Პარაგრაფი 3: კონტრარგუმენტი ან დამატებითი დეტალი</string>
    <string name="essay_outline_conclusion_title" translate_by="gpt">3. დასკვნა</string>
    <string name="essay_outline_conclusion_summary" translate_by="gpt">- მთავარი პუნქტების შეჯამება</string>
    <string name="essay_outline_conclusion_restate" translate_by="gpt">- თეზისის ახალი ფორმულირება</string>
    <string name="essay_outline_conclusion_final" translate_by="gpt">- დაასრულეთ ძლიერი საბოლოო აზრით</string>
    <string name="essay_outline_notes_title" translate_by="gpt">✨ შენიშვნები:</string>
    <string name="essay_outline_notes_tone" translate_by="gpt">- შეინარჩუნეთ %1$s ტონი throughout</string>
    <string name="essay_outline_notes_wordcount" translate_by="gemini">Მიზნად ისახეთ დაახლოებით %1$s ჯამში</string>
    <string name="essay_outline_notes_structure" translate_by="gemini">Მიჰყევით ტიპურ %1$s ესეს სტრუქტურას</string>
    <string name="literature_outline_title_label" translate_by="gpt">Სათაური: %1$s</string>
    <string name="literature_outline_author_label" translate_by="gpt">Ავტორი: %1$s</string>
    <string name="literature_outline_focus_label" translate_by="gemini">Ფოკუსი: %1$s</string>
    <string name="literature_outline_length_label" translate_by="gpt">Სიგრძე: %1$s</string>
    <string name="literature_outline_academic_level_label" translate_by="gpt">Აკადემიური დონე: %1$s</string>
    <string name="literature_outline_outline_label" translate_by="gemini">Მონახაზი:</string>
    <string name="literature_outline_introduction_title" translate_by="gpt">1. შესავალი</string>
    <string name="literature_outline_introduction_context" translate_by="gemini">Წარმოადგინეთ ლიტერატურული ნაწარმოები და მისი კონტექსტი.</string>
    <string name="literature_outline_introduction_author" translate_by="google">ახსენეთ ავტორი და შესაბამისობა არჩეული ანალიზის ფოკუსთან.</string>
    <string name="literature_outline_background_title" translate_by="gpt">2. ფონი</string>
    <string name="literature_outline_background_summary" translate_by="gpt">Სიუჟეტის ან მთავარი პერსონაჟების შეჯამება (ანალიზის ტიპის მიხედვით).</string>
    <string name="literature_outline_background_context" translate_by="gemini">Მოგვაწოდეთ საჭირო კონტექსტი უფრო ღრმა ანალიზისთვის.</string>
    <string name="literature_outline_analysis_title" translate_by="gemini">3. ძირითადი ანალიზი</string>
    <string name="literature_outline_analysis_deep_dive" translate_by="gemini">Ღრმად ჩაყვინთვა: %1$s.</string>
    <string name="literature_outline_analysis_evidence" translate_by="gemini">Გამოიყენეთ ტექსტიდან მტკიცებულებები: ციტატები, მოვლენები, სიმბოლიზმი და ა.შ.</string>
    <string name="literature_outline_connections_title" translate_by="gpt">4. კავშირები</string>
    <string name="literature_outline_connections_themes" translate_by="gemini">Თემებთან ან რეალურ სამყაროში არსებულ შედეგებთან დაკავშირება.</string>
    <string name="literature_outline_connections_contrast" translate_by="gemini">Სურვილისამებრ შეადარეთ სხვა პერსონაჟებს ან ნამუშევრებს.</string>
    <string name="literature_outline_conclusion_title" translate_by="gpt">5. დასკვნა</string>
    <string name="literature_outline_conclusion_insights" translate_by="gemini">Ძირითადი დასკვნების შეჯამება.</string>
    <string name="literature_outline_conclusion_value" translate_by="gemini">Შეაფასეთ სამუშაოს ღირებულება აკადემიური პერსპექტივიდან.</string>
</resources>