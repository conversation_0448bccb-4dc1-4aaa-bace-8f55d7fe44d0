<?xml version='1.0' encoding='utf-8'?>
<resources>
    <string name="generate_outline" translate_by="gemini">สร้างโครงร่าง</string>
    <string name="placeholder_default" translate_by="gemini">พิมพ์ข้อความที่นี่…</string>
    <string name="essay_screen_description" translate_by="gemini">แน่นอน! ฉันช่วยได้ โปรดให้รายละเอียดเกี่ยวกับเรียงความของคุณด้านล่างนี้</string>
    <string name="label_choose_topic" translate_by="gpt">เลือกหัวข้อ</string>
    <string name="label_essay_type" translate_by="gpt">ประเภทของเรียงความ</string>
    <string name="label_word_count" translate_by="gpt">จำนวนคำ</string>
    <string name="label_language_tone" translate_by="gpt">ภาษา + น้ำเสียง</string>
    <string name="placeholder_topic" translate_by="gpt">อธิบายสถานที่ที่ทำให้คุณรู้สึกสงบ</string>
    <string name="placeholder_essay_type" translate_by="gpt">ตัวอย่าง: โต้แย้ง, เล่าเรื่อง…</string>
    <string name="placeholder_word_count" translate_by="gemini">เช่น 300 คำ, 500 คำ, 1000 คำ…</string>
    <string name="placeholder_language_tone" translate_by="gpt">ตัวอย่าง: เป็นทางการ, ทางวิชาการ, …</string>
    <string name="research_screen_description" translate_by="gemini">พื้นที่สำหรับเปลี่ยนข้อมูลดิบให้เป็นเรื่องราวที่มีความหมายและมองเห็นได้ผ่านการวิจัยและการวิเคราะห์</string>
    <string name="label_research_topic" translate_by="gemini">หัวข้อวิจัย</string>
    <string name="label_research_goal" translate_by="gpt">เป้าหมายการวิจัย</string>
    <string name="label_preferred_sources" translate_by="gpt">แหล่งข้อมูลที่ต้องการ</string>
    <string name="label_depth_length" translate_by="gpt">ความลึก / ความยาว</string>
    <string name="label_academic_level" translate_by="gpt">ระดับการศึกษา</string>
    <string name="placeholder_research_topic" translate_by="gpt">ตัวอย่าง: การเปลี่ยนแปลงสภาพภูมิอากาศ, ผลกระทบของ AI ต่อการจ้างงาน, …</string>
    <string name="placeholder_research_goal" translate_by="gpt">การรวบรวมข้อมูล, การวิเคราะห์แนวโน้ม…</string>
    <string name="placeholder_preferred_sources" translate_by="gpt">ตัวอย่าง: วารสารวิทยาศาสตร์, หนังสือ, บทความทางการ</string>
    <string name="placeholder_depth_length" translate_by="gemini">เช่น 300 คำ, 500 คำ, 1000 คำ…</string>
    <string name="placeholder_academic_level" translate_by="gpt">นักเรียนมัธยมปลาย, นักศึกษา, การวิจัยขั้นสูง, …</string>
    <string name="literature_screen_description" translate_by="gpt">จากคำพูดสู่ความหมายที่ซ่อนอยู่ เราช่วยให้คุณค้นพบคุณค่าที่แท้จริงของงานวรรณกรรมทุกชิ้น</string>
    <string name="label_title_of_work" translate_by="gpt">ชื่อผลงาน</string>
    <string name="label_author" translate_by="gpt">ผู้เขียน</string>
    <string name="label_analysis_type" translate_by="gpt">คุณต้องการวิเคราะห์อะไร?</string>
    <string name="label_format" translate_by="gpt">ความยาว / รูปแบบ</string>
    <string name="placeholder_title" translate_by="gpt">ตัวอย่าง: เดอะ เกรท แกตสบี้</string>
    <string name="placeholder_author" translate_by="google">ตัวอย่าง: F. Scott Fitzgerald</string>
    <string name="placeholder_analysis_type" translate_by="gpt">การวิเคราะห์ตัวละคร, ธีมหลัก…</string>
    <string name="placeholder_format" translate_by="gemini">เช่น 300 คำ, 500 คำ, 1000 คำ…</string>
    <string name="placeholder_academic_level_literature" translate_by="gpt">ตัวอย่าง: โรงเรียนมัธยมต้น, โรงเรียนมัธยมปลาย หรือมหาวิทยาลัย, …</string>
    <string name="research_outline_topic_label" translate_by="gemini">📘 หัวข้อวิจัย: %1$s</string>
    <string name="research_outline_goal_label" translate_by="gpt">🎯 เป้าหมายการวิจัย: %1$s</string>
    <string name="research_outline_sources_label" translate_by="gpt">📚 แหล่งข้อมูลที่ต้องการ: %1$s</string>
    <string name="research_outline_depth_label" translate_by="gpt">📏 ความลึก/ความยาว: %1$s</string>
    <string name="research_outline_academic_level_label" translate_by="gpt">🎓 ระดับการศึกษา: %1$s</string>
    <string name="research_outline_suggested_outline_label" translate_by="gemini">🧾 โครงร่างแนะนำ:</string>
    <string name="research_outline_introduction_title" translate_by="gemini">1. บทนำ</string>
    <string name="research_outline_introduction_overview" translate_by="gemini">ภาพรวมโดยย่อของ %1$s</string>
    <string name="research_outline_introduction_importance" translate_by="gemini">ความสำคัญของการวิจัยในระดับ %1$s</string>
    <string name="research_outline_objectives_title" translate_by="gpt">2. วัตถุประสงค์</string>
    <string name="research_outline_objectives_goal" translate_by="gpt">- ชี้แจงเป้าหมายหลัก: %1$s</string>
    <string name="research_outline_methodology_title" translate_by="google">3. วิธีการ</string>
    <string name="research_outline_methodology_approach" translate_by="google">- แนวทางการวิจัย</string>
    <string name="research_outline_methodology_sources" translate_by="gpt">- แหล่งข้อมูล: %1$s</string>
    <string name="research_outline_key_insights_title" translate_by="gemini">4. ข้อมูลเชิงลึกที่สำคัญ</string>
    <string name="research_outline_key_insights_trends" translate_by="gpt">- อภิปรายแนวโน้ม ข้อเท็จจริง หรือผลการวิเคราะห์</string>
    <string name="research_outline_key_insights_citations" translate_by="gpt">- ใช้การอ้างอิงหากจำเป็น</string>
    <string name="research_outline_conclusion_title" translate_by="gemini">5. บทสรุป</string>
    <string name="research_outline_conclusion_summary" translate_by="gemini">สรุปผลการค้นพบ</string>
    <string name="research_outline_conclusion_implications" translate_by="gpt">- ผลกระทบหรือการทำงานในอนาคต</string>
    <string name="essay_outline_topic_label" translate_by="gpt">✏️ หัวข้อเรียงความ: %1$s</string>
    <string name="essay_outline_type_label" translate_by="gpt">📝 ประเภทเรียงความ: %1$s</string>
    <string name="essay_outline_word_count_label" translate_by="gpt">🔢 จำนวนคำ: %1$s</string>
    <string name="essay_outline_language_tone_label" translate_by="gpt">🗣️ ภาษาและโทน: %1$s</string>
    <string name="essay_outline_suggested_outline_label" translate_by="gpt">🧾 แนะนำโครงร่าง:</string>
    <string name="essay_outline_introduction_title" translate_by="gpt">1. บทนำ</string>
    <string name="essay_outline_introduction_topic" translate_by="gpt">- แนะนำหัวข้อ: %1$s</string>
    <string name="essay_outline_introduction_background" translate_by="gemini">ให้ข้อมูลพื้นฐาน/บริบท</string>
    <string name="essay_outline_introduction_thesis" translate_by="gpt">- ระบุวิทยานิพนธ์</string>
    <string name="essay_outline_body_title" translate_by="gpt">2. ย่อหน้าหลัก</string>
    <string name="essay_outline_body_paragraph1" translate_by="gemini">ย่อหน้า 1: ข้อโต้แย้งหรือประเด็นแรก</string>
    <string name="essay_outline_body_paragraph2" translate_by="gpt">- ย่อหน้าที่ 2: หลักฐานหรือเรื่องราวที่สนับสนุน</string>
    <string name="essay_outline_body_paragraph3" translate_by="gpt">- ย่อหน้าที่ 3: ข้อโต้แย้งหรือรายละเอียดเพิ่มเติม</string>
    <string name="essay_outline_conclusion_title" translate_by="gemini">3. บทสรุป</string>
    <string name="essay_outline_conclusion_summary" translate_by="gpt">- สรุปประเด็นสำคัญ</string>
    <string name="essay_outline_conclusion_restate" translate_by="gpt">- กล่าวซ้ำวิทยานิพนธ์ในรูปแบบใหม่</string>
    <string name="essay_outline_conclusion_final" translate_by="google">- สรุปด้วยความคิดสุดท้ายที่แข็งแกร่ง</string>
    <string name="essay_outline_notes_title" translate_by="gpt">✨ หมายเหตุ:</string>
    <string name="essay_outline_notes_tone" translate_by="gemini">ใช้โทน %1$s อย่างสม่ำเสมอ</string>
    <string name="essay_outline_notes_wordcount" translate_by="gpt">- ตั้งเป้าหมายประมาณ %1$s รวมทั้งหมด</string>
    <string name="essay_outline_notes_structure" translate_by="gpt">- ปฏิบัติตามโครงสร้างเรียงความแบบทั่วไป %1$s</string>
    <string name="literature_outline_title_label" translate_by="gpt">ชื่อ: %1$s</string>
    <string name="literature_outline_author_label" translate_by="gpt">ผู้เขียน: %1$s</string>
    <string name="literature_outline_focus_label" translate_by="gpt">โฟกัส: %1$s</string>
    <string name="literature_outline_length_label" translate_by="gpt">ความยาว: %1$s</string>
    <string name="literature_outline_academic_level_label" translate_by="gpt">ระดับการศึกษา: %1$s</string>
    <string name="literature_outline_outline_label" translate_by="gpt">โครงร่าง:</string>
    <string name="literature_outline_introduction_title" translate_by="gpt">1. บทนำ</string>
    <string name="literature_outline_introduction_context" translate_by="gpt">แนะนำงานวรรณกรรมและบริบทของมัน</string>
    <string name="literature_outline_introduction_author" translate_by="gemini">กล่าวถึงผู้แต่งและความเกี่ยวข้องกับประเด็นการวิเคราะห์ที่เลือก</string>
    <string name="literature_outline_background_title" translate_by="gpt">2. พื้นหลัง</string>
    <string name="literature_outline_background_summary" translate_by="gemini">สรุปเนื้อเรื่องหรือตัวละครหลัก (ขึ้นอยู่กับประเภทการวิเคราะห์)</string>
    <string name="literature_outline_background_context" translate_by="gemini">ให้ข้อมูลที่จำเป็นสำหรับวิเคราะห์เชิงลึก</string>
    <string name="literature_outline_analysis_title" translate_by="gpt">3. การวิเคราะห์หลัก</string>
    <string name="literature_outline_analysis_deep_dive" translate_by="gemini">เจาะลึก: %1$s</string>
    <string name="literature_outline_analysis_evidence" translate_by="gpt">ใช้หลักฐานจากข้อความ: คำพูด, เหตุการณ์, สัญลักษณ์, ฯลฯ</string>
    <string name="literature_outline_connections_title" translate_by="gpt">4. การเชื่อมต่อ</string>
    <string name="literature_outline_connections_themes" translate_by="gemini">เชื่อมโยงการวิเคราะห์กับหัวข้อที่ใหญ่ขึ้นหรือผลกระทบในโลกแห่งความเป็นจริง</string>
    <string name="literature_outline_connections_contrast" translate_by="gpt">อาจเปรียบเทียบกับตัวละครหรือผลงานอื่น ๆ ได้</string>
    <string name="literature_outline_conclusion_title" translate_by="gemini">5. บทสรุป</string>
    <string name="literature_outline_conclusion_insights" translate_by="gemini">สรุปประเด็นสำคัญ</string>
    <string name="literature_outline_conclusion_value" translate_by="gpt">สะท้อนถึงคุณค่าของงานจากมุมมองทางวิชาการ</string>
</resources>