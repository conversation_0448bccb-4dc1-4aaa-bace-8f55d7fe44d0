<?xml version='1.0' encoding='utf-8'?>
<resources>
    <string name="generate_outline" translate_by="gpt">개요 생성</string>
    <string name="placeholder_default" translate_by="gpt">예: 여기에 텍스트를 입력하세요…</string>
    <string name="essay_screen_description" translate_by="gemini">네! 도와드릴 수 있습니다. 아래에 에세이에 대한 세부 정보를 제공해 주세요.</string>
    <string name="label_choose_topic" translate_by="gemini">주제 선택</string>
    <string name="label_essay_type" translate_by="gpt">에세이 유형</string>
    <string name="label_word_count" translate_by="gpt">단어 수</string>
    <string name="label_language_tone" translate_by="gpt">언어 + 톤</string>
    <string name="placeholder_topic" translate_by="gemini">Ex: 평화로운 기분이 드는 장소를 묘사해 보세요</string>
    <string name="placeholder_essay_type" translate_by="gpt">예: 논쟁적, 서사적…</string>
    <string name="placeholder_word_count" translate_by="gpt">예: 300 단어, 500 단어, 1000 단어…</string>
    <string name="placeholder_language_tone" translate_by="gpt">예: 공식적인, 학문적인, …</string>
    <string name="research_screen_description" translate_by="gemini">연구 및 분석을 통해 원시 데이터를 의미 있고 시각적인 스토리로 바꿀 수 있는 공간입니다.</string>
    <string name="label_research_topic" translate_by="gpt">연구 주제</string>
    <string name="label_research_goal" translate_by="gpt">연구 목표</string>
    <string name="label_preferred_sources" translate_by="gpt">선호하는 출처</string>
    <string name="label_depth_length" translate_by="gpt">깊이 / 길이</string>
    <string name="label_academic_level" translate_by="gpt">학업 수준</string>
    <string name="placeholder_research_topic" translate_by="gpt">예: 기후 변화, AI가 일자리에 미치는 영향, …</string>
    <string name="placeholder_research_goal" translate_by="google">예 : 정보 수집, 트렌드 분석 …</string>
    <string name="placeholder_preferred_sources" translate_by="google">예 : 과학 저널, 서적, 공식 기사</string>
    <string name="placeholder_depth_length" translate_by="google">예 : 300 단어, 500 단어, 1000 단어 …</string>
    <string name="placeholder_academic_level" translate_by="gemini">고등학생, 대학생, 고급 연구 등</string>
    <string name="literature_screen_description" translate_by="gpt">단어에서 숨겨진 의미까지, 우리는 모든 문학 작품의 진정한 가치를 발견하는 데 도움을 줍니다.</string>
    <string name="label_title_of_work" translate_by="gpt">작품 제목</string>
    <string name="label_author" translate_by="gpt">저자</string>
    <string name="label_analysis_type" translate_by="gpt">무엇을 분석하고 싶으신가요?</string>
    <string name="label_format" translate_by="gpt">길이 / 형식</string>
    <string name="placeholder_title" translate_by="gpt">위대한 개츠비</string>
    <string name="placeholder_author" translate_by="gpt">F. 스콧 피츠제럴드</string>
    <string name="placeholder_analysis_type" translate_by="google">예 : 캐릭터 분석, 주요 테마 …</string>
    <string name="placeholder_format" translate_by="google">예 : 300 단어, 500 단어, 1000 단어 …</string>
    <string name="placeholder_academic_level_literature" translate_by="gemini">중학교, 고등학교 또는 대학교 등</string>
    <string name="research_outline_topic_label" translate_by="gpt">📘 연구 주제: %1$s</string>
    <string name="research_outline_goal_label" translate_by="gpt">🎯 연구 목표: %1$s</string>
    <string name="research_outline_sources_label" translate_by="gpt">📚 선호하는 출처: %1$s</string>
    <string name="research_outline_depth_label" translate_by="gpt">📏 깊이/길이: %1$s</string>
    <string name="research_outline_academic_level_label" translate_by="gemini">🎓 학력: %1$s</string>
    <string name="research_outline_suggested_outline_label" translate_by="gemini">🧾 추천 개요:</string>
    <string name="research_outline_introduction_title" translate_by="gpt">1. 소개</string>
    <string name="research_outline_introduction_overview" translate_by="gpt">- %1$s에 대한 간략한 개요</string>
    <string name="research_outline_introduction_importance" translate_by="gemini">%1$s 수준에서의 연구 중요성</string>
    <string name="research_outline_objectives_title" translate_by="gpt">2. 목표</string>
    <string name="research_outline_objectives_goal" translate_by="gemini">- 주요 목표 명확화: %1$s</string>
    <string name="research_outline_methodology_title" translate_by="gpt">3. 방법론</string>
    <string name="research_outline_methodology_approach" translate_by="gpt">연구 접근법</string>
    <string name="research_outline_methodology_sources" translate_by="gpt">- 데이터 소스: %1$s</string>
    <string name="research_outline_key_insights_title" translate_by="gpt">4. 주요 통찰력</string>
    <string name="research_outline_key_insights_trends" translate_by="gpt">- 트렌드, 사실 또는 분석 결과에 대해 논의하기</string>
    <string name="research_outline_key_insights_citations" translate_by="gpt">- 필요시 인용을 사용하세요</string>
    <string name="research_outline_conclusion_title" translate_by="gpt">5. 결론</string>
    <string name="research_outline_conclusion_summary" translate_by="google">- 결과 요약</string>
    <string name="research_outline_conclusion_implications" translate_by="gemini">시사점 또는 향후 연구</string>
    <string name="essay_outline_topic_label" translate_by="gpt">✏️ 에세이 주제: %1$s</string>
    <string name="essay_outline_type_label" translate_by="gpt">📝 에세이 유형: %1$s</string>
    <string name="essay_outline_word_count_label" translate_by="gpt">🔢 단어 수: %1$s</string>
    <string name="essay_outline_language_tone_label" translate_by="gpt">🗣️ 언어 및 톤: %1$s</string>
    <string name="essay_outline_suggested_outline_label" translate_by="gemini">🧾 추천 개요:</string>
    <string name="essay_outline_introduction_title" translate_by="gpt">1. 소개</string>
    <string name="essay_outline_introduction_topic" translate_by="gpt">- 주제 소개: %1$s</string>
    <string name="essay_outline_introduction_background" translate_by="gpt">배경/맥락 제공</string>
    <string name="essay_outline_introduction_thesis" translate_by="gemini">논제를 진술하세요</string>
    <string name="essay_outline_body_title" translate_by="gpt">2. 본문 단락</string>
    <string name="essay_outline_body_paragraph1" translate_by="gpt">- 단락 1: 첫 번째 주장 또는 요점</string>
    <string name="essay_outline_body_paragraph2" translate_by="gemini">단락 2: 뒷받침 증거 또는 설명</string>
    <string name="essay_outline_body_paragraph3" translate_by="gpt">- 3단락: 반론 또는 추가 세부사항</string>
    <string name="essay_outline_conclusion_title" translate_by="gpt">3. 결론</string>
    <string name="essay_outline_conclusion_summary" translate_by="gpt">주요 사항 요약</string>
    <string name="essay_outline_conclusion_restate" translate_by="gpt">- 새로운 방식으로 주제를 재진술하세요</string>
    <string name="essay_outline_conclusion_final" translate_by="gpt">강력한 결론으로 마무리하세요.</string>
    <string name="essay_outline_notes_title" translate_by="gemini">✨ 메모:</string>
    <string name="essay_outline_notes_tone" translate_by="gemini">- %1$s 어조를 유지하세요</string>
    <string name="essay_outline_notes_wordcount" translate_by="gemini">총 %1$s 정도를 목표로 하세요</string>
    <string name="essay_outline_notes_structure" translate_by="gemini">일반적인 %1$s 에세이 구조를 따르세요</string>
    <string name="literature_outline_title_label" translate_by="gpt">제목: %1$s</string>
    <string name="literature_outline_author_label" translate_by="gpt">저자: %1$s</string>
    <string name="literature_outline_focus_label" translate_by="gpt">초점: %1$s</string>
    <string name="literature_outline_length_label" translate_by="gpt">길이: %1$s</string>
    <string name="literature_outline_academic_level_label" translate_by="gemini">학력: %1$s</string>
    <string name="literature_outline_outline_label" translate_by="gpt">개요:</string>
    <string name="literature_outline_introduction_title" translate_by="gpt">1. 소개</string>
    <string name="literature_outline_introduction_context" translate_by="google">문학 작품과 맥락을 소개합니다.</string>
    <string name="literature_outline_introduction_author" translate_by="gpt">저자와 선택한 분석 초점과의 관련성을 언급하세요.</string>
    <string name="literature_outline_background_title" translate_by="gpt">2. 배경</string>
    <string name="literature_outline_background_summary" translate_by="gemini">줄거리 또는 주요 등장인물 요약 (분석 유형에 따라 다름)</string>
    <string name="literature_outline_background_context" translate_by="gemini">더 깊은 분석을 위한 필요한 맥락을 제공하세요.</string>
    <string name="literature_outline_analysis_title" translate_by="gpt">3. 주요 분석</string>
    <string name="literature_outline_analysis_deep_dive" translate_by="gpt">심층 탐색: %1$s.</string>
    <string name="literature_outline_analysis_evidence" translate_by="gemini">텍스트에서 증거 사용: 인용문, 사건, 상징 등</string>
    <string name="literature_outline_connections_title" translate_by="gpt">4. 연결</string>
    <string name="literature_outline_connections_themes" translate_by="gpt">더 큰 주제나 실제 세계의 함의에 대한 링크 분석.</string>
    <string name="literature_outline_connections_contrast" translate_by="gemini">선택적으로 다른 캐릭터나 작품과 대조해 보세요.</string>
    <string name="literature_outline_conclusion_title" translate_by="gpt">5. 결론</string>
    <string name="literature_outline_conclusion_insights" translate_by="gpt">주요 통찰을 재진술하십시오.</string>
    <string name="literature_outline_conclusion_value" translate_by="gpt">학문적 관점에서 작업의 가치를 반영하십시오.</string>
</resources>