<?xml version='1.0' encoding='utf-8'?>
<resources>
    <string name="generate_outline" translate_by="gpt"><PERSON><PERSON><PERSON> esquema</string>
    <string name="placeholder_default" translate_by="gemini">Introduce aquí o teu texto…</string>
    <string name="essay_screen_description" translate_by="gemini">Claro! Podo axudarche con iso. Por favor, proporciona algúns detalles para o teu ensaio a continuación.</string>
    <string name="label_choose_topic" translate_by="gpt">Elixe un tema</string>
    <string name="label_essay_type" translate_by="gpt">Tipo de ensaio</string>
    <string name="label_word_count" translate_by="gpt">Contador de palabras</string>
    <string name="label_language_tone" translate_by="gemini">Idioma e ton</string>
    <string name="placeholder_topic" translate_by="gemini">Describe un lugar que che dea paz</string>
    <string name="placeholder_essay_type" translate_by="gpt">Ex: Argumentativo, Narrativo…</string>
    <string name="placeholder_word_count" translate_by="gpt">Ex: 300 palabras, 500 palabras, 1000 palabras…</string>
    <string name="placeholder_language_tone" translate_by="gpt">Ex: Formal, académico, …</string>
    <string name="research_screen_description" translate_by="gemini">Un lugar para transformar datos brutos en historias visuais significativas a través da investigación e a análise.</string>
    <string name="label_research_topic" translate_by="gpt">Tema de investigación</string>
    <string name="label_research_goal" translate_by="gpt">Obxectivo da investigación</string>
    <string name="label_preferred_sources" translate_by="gpt">Fontes preferidas</string>
    <string name="label_depth_length" translate_by="gemini">Profundidade / Lonxitude</string>
    <string name="label_academic_level" translate_by="gpt">Nivel académico</string>
    <string name="placeholder_research_topic" translate_by="gpt">Ex: Cambio climático, O impacto da IA nos empregos, …</string>
    <string name="placeholder_research_goal" translate_by="gemini">Recollida de información, análise de tendencias…</string>
    <string name="placeholder_preferred_sources" translate_by="gpt">Ex: Revistas científicas, libros, artigos oficiais</string>
    <string name="placeholder_depth_length" translate_by="gpt">Ex: 300 palabras, 500 palabras, 1000 palabras…</string>
    <string name="placeholder_academic_level" translate_by="gpt">Ex: Estudantes de secundaria, estudantes universitarios, investigación avanzada, …</string>
    <string name="literature_screen_description" translate_by="gpt">Desde as palabras até os significados ocultos, axudámosche a descubrir o verdadeiro valor de cada obra literaria.</string>
    <string name="label_title_of_work" translate_by="gpt">Título da obra</string>
    <string name="label_author" translate_by="gpt">Autor</string>
    <string name="label_analysis_type" translate_by="gpt">Que queres analizar?</string>
    <string name="label_format" translate_by="google">Lonxitude / formato</string>
    <string name="placeholder_title" translate_by="gpt">O Gran Gatsby</string>
    <string name="placeholder_author" translate_by="gpt">Ex: F. Scott Fitzgerald</string>
    <string name="placeholder_analysis_type" translate_by="gpt">Ex: Análise de personaxes, Temas principais…</string>
    <string name="placeholder_format" translate_by="gpt">Ex: 300 palabras, 500 palabras, 1000 palabras…</string>
    <string name="placeholder_academic_level_literature" translate_by="gpt">Ex: Escola secundaria, instituto ou universidade, …</string>
    <string name="research_outline_topic_label" translate_by="gpt">📘 Tema de investigación: %1$s</string>
    <string name="research_outline_goal_label" translate_by="gpt">🎯 Obxectivo da investigación: %1$s</string>
    <string name="research_outline_sources_label" translate_by="gpt">📚 Fontes preferidas: %1$s</string>
    <string name="research_outline_depth_label" translate_by="gemini">📏 Profundidade/Lonxitude: %1$s</string>
    <string name="research_outline_academic_level_label" translate_by="gpt">🎓 Nivel Académico: %1$s</string>
    <string name="research_outline_suggested_outline_label" translate_by="gemini">🧾 Esquema Suxerido:</string>
    <string name="research_outline_introduction_title" translate_by="gpt">1. Introdución</string>
    <string name="research_outline_introduction_overview" translate_by="gemini">Breve resumo de %1$s</string>
    <string name="research_outline_introduction_importance" translate_by="gemini">Importancia da investigación a nivel de %1$s</string>
    <string name="research_outline_objectives_title" translate_by="gpt">2. Obxectivos</string>
    <string name="research_outline_objectives_goal" translate_by="gpt">- Aclarar o obxectivo principal: %1$s</string>
    <string name="research_outline_methodology_title" translate_by="gpt">3. Metodoloxía</string>
    <string name="research_outline_methodology_approach" translate_by="gpt">- Enfoque de investigación</string>
    <string name="research_outline_methodology_sources" translate_by="gpt">- Fontes de datos: %1$s</string>
    <string name="research_outline_key_insights_title" translate_by="gemini">4. Ideas clave</string>
    <string name="research_outline_key_insights_trends" translate_by="gemini">Debater tendencias, feitos ou resultados da análise</string>
    <string name="research_outline_key_insights_citations" translate_by="gemini">Usa citas se é necesario</string>
    <string name="research_outline_conclusion_title" translate_by="gpt">5. Conclusión</string>
    <string name="research_outline_conclusion_summary" translate_by="gpt">- Resumo dos achados</string>
    <string name="research_outline_conclusion_implications" translate_by="gpt">- Implicacións ou traballo futuro</string>
    <string name="essay_outline_topic_label" translate_by="gpt">✏️ Tema do ensaio: %1$s</string>
    <string name="essay_outline_type_label" translate_by="gpt">📝 Tipo de ensaio: %1$s</string>
    <string name="essay_outline_word_count_label" translate_by="gpt">🔢 Contador de palabras: %1$s</string>
    <string name="essay_outline_language_tone_label" translate_by="gpt">🗣️ Idioma e Ton: %1$s</string>
    <string name="essay_outline_suggested_outline_label" translate_by="gemini">🧾 Esquema Suxerido:</string>
    <string name="essay_outline_introduction_title" translate_by="gpt">1. Introdución</string>
    <string name="essay_outline_introduction_topic" translate_by="gpt">- Introduce o tema: %1$s</string>
    <string name="essay_outline_introduction_background" translate_by="gpt">- Proporcionar antecedentes/contexto</string>
    <string name="essay_outline_introduction_thesis" translate_by="google">- Indique a tese</string>
    <string name="essay_outline_body_title" translate_by="gpt">2. Parágrafos do corpo</string>
    <string name="essay_outline_body_paragraph1" translate_by="gpt">- Parágrafo 1: Primeiro argumento ou punto</string>
    <string name="essay_outline_body_paragraph2" translate_by="gemini">Parágrafo 2: Probas ou narración de apoio</string>
    <string name="essay_outline_body_paragraph3" translate_by="gpt">- Parágrafo 3: Contraargumento ou detalle adicional</string>
    <string name="essay_outline_conclusion_title" translate_by="gpt">3. Conclusión</string>
    <string name="essay_outline_conclusion_summary" translate_by="google">- Resume os puntos clave</string>
    <string name="essay_outline_conclusion_restate" translate_by="gemini">Reformula a tese de xeito diferente</string>
    <string name="essay_outline_conclusion_final" translate_by="gemini">Conclúe cunha reflexión final impactante</string>
    <string name="essay_outline_notes_title" translate_by="gpt">✨ Notas:</string>
    <string name="essay_outline_notes_tone" translate_by="gemini">Mantén un ton %1$s en todo momento</string>
    <string name="essay_outline_notes_wordcount" translate_by="gemini">Apunta a aproximadamente %1$s en total</string>
    <string name="essay_outline_notes_structure" translate_by="gpt">- Segue a estrutura típica do ensaio %1$s</string>
    <string name="literature_outline_title_label" translate_by="gpt">Título: %1$s</string>
    <string name="literature_outline_author_label" translate_by="gpt">Autor: %1$s</string>
    <string name="literature_outline_focus_label" translate_by="gemini">Foco: %1$s</string>
    <string name="literature_outline_length_label" translate_by="gpt">Lonxitude: %1$s</string>
    <string name="literature_outline_academic_level_label" translate_by="gpt">Nivel Académico: %1$s</string>
    <string name="literature_outline_outline_label" translate_by="gemini">Esquema:</string>
    <string name="literature_outline_introduction_title" translate_by="gpt">1. Introdución</string>
    <string name="literature_outline_introduction_context" translate_by="gpt">Introduce a obra literaria e o seu contexto.</string>
    <string name="literature_outline_introduction_author" translate_by="gemini">Menciona o autor e a relevancia para o foco de análise escollido.</string>
    <string name="literature_outline_background_title" translate_by="gpt">2. Antecedentes</string>
    <string name="literature_outline_background_summary" translate_by="gpt">Resumo da trama ou personaxes clave (dependendo do tipo de análise).</string>
    <string name="literature_outline_background_context" translate_by="gpt">Proporciona o contexto necesario para unha análise máis profunda.</string>
    <string name="literature_outline_analysis_title" translate_by="gpt">3. Análise Principal</string>
    <string name="literature_outline_analysis_deep_dive" translate_by="gpt">Mergúllate en: %1$s.</string>
    <string name="literature_outline_analysis_evidence" translate_by="gpt">Usa evidencia do texto: citas, eventos, simbolismo, etc.</string>
    <string name="literature_outline_connections_title" translate_by="gpt">4. Conexións</string>
    <string name="literature_outline_connections_themes" translate_by="gemini">Análise de ligazóns a temas máis amplos ou implicacións do mundo real.</string>
    <string name="literature_outline_connections_contrast" translate_by="gpt">Opcionalmente contrasta con outros personaxes ou obras.</string>
    <string name="literature_outline_conclusion_title" translate_by="gpt">5. Conclusión</string>
    <string name="literature_outline_conclusion_insights" translate_by="gpt">Reformula as ideas clave.</string>
    <string name="literature_outline_conclusion_value" translate_by="gpt">Reflexiona sobre o valor do traballo desde unha perspectiva académica.</string>
</resources>