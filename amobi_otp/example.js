const TOTPValidator = require('./otp_validator');

// Initialize with the same secret as your Android app
const secret = 'JBSWY3DPEHPK3PXP';  // Replace with your secret
const validator = new TOTPValidator(secret);

// Function to validate OTP
function validateOTP(userCode) {
    // Verify with ±30 second window (window=1 means one period before and after)
    const isValid = validator.verify(userCode, undefined, 1);
    console.log(`Code ${userCode} is ${isValid ? 'valid' : 'invalid'}`);
    return isValid;
}

// Example usage:
const currentCode = validator.generate();
console.log('Current code:', currentCode);

// Test validation
validateOTP(currentCode);  // Should be valid
validateOTP('000000');    // Should be invalid 