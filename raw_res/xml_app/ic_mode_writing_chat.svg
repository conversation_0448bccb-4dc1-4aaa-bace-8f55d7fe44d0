<?xml version="1.0" encoding="utf-8"?>
<svg xmlns="http://www.w3.org/2000/svg" width="24.0" height="24.0" viewBox="0 0 24.0 24.0">
    <defs>
        <linearGradient id="gradient_0" x1="0.0" y1="0.0" x2="1.0" y2="1.0">
            <stop offset="0" stop-color="#FF7044"/>
            <stop offset="1" stop-color="#F92814"/>
        </linearGradient>
        <linearGradient id="gradient_1" x1="0.0" y1="0.0" x2="1.0" y2="1.0">
            <stop offset="0" stop-color="#F92814"/>
            <stop offset="1" stop-color="#C1272D"/>
        </linearGradient>
        <linearGradient id="gradient_2" x1="0.0" y1="0.0" x2="1.0" y2="1.0">
            <stop offset="0" stop-color="#F9F7FC"/>
            <stop offset="1" stop-color="#F0DDFC"/>
        </linearGradient>
        <linearGradient id="gradient_3" x1="0.0" y1="0.0" x2="1.0" y2="1.0">
            <stop offset="0" stop-color="#F9F7FC"/>
            <stop offset="1" stop-color="#F0DDFC"/>
        </linearGradient>
        <linearGradient id="gradient_4" x1="0.0" y1="0.0" x2="1.0" y2="1.0">
            <stop offset="0" stop-color="#F0DDFC"/>
            <stop offset="0.289" stop-color="#C8B7E0"/>
            <stop offset="0.592" stop-color="#A595C8"/>
            <stop offset="0.84" stop-color="#8F81B8"/>
            <stop offset="1" stop-color="#8779B3"/>
        </linearGradient>
        <linearGradient id="gradient_5" x1="0.0" y1="0.0" x2="1.0" y2="1.0">
            <stop offset="0" stop-color="#F0DDFC"/>
            <stop offset="0.289" stop-color="#C8B7E0"/>
            <stop offset="0.592" stop-color="#A595C8"/>
            <stop offset="0.84" stop-color="#8F81B8"/>
            <stop offset="1" stop-color="#8779B3"/>
        </linearGradient>
        <linearGradient id="gradient_6" x1="0.0" y1="0.0" x2="1.0" y2="1.0">
            <stop offset="0" stop-color="#F9F7FC"/>
            <stop offset="1" stop-color="#F0DDFC"/>
        </linearGradient>
        <linearGradient id="gradient_7" x1="0.0" y1="0.0" x2="1.0" y2="1.0">
            <stop offset="0" stop-color="#F9F7FC"/>
            <stop offset="1" stop-color="#F0DDFC"/>
        </linearGradient>
        <linearGradient id="gradient_8" x1="0.0" y1="0.0" x2="1.0" y2="1.0">
            <stop offset="0" stop-color="#E9EDF5"/>
            <stop offset="1" stop-color="#FFFFFF"/>
        </linearGradient>
        <linearGradient id="gradient_9" x1="0.0" y1="0.0" x2="1.0" y2="1.0">
            <stop offset="0" stop-color="#F0DDFC"/>
            <stop offset="0.161" stop-color="#E8D5F6"/>
            <stop offset="0.417" stop-color="#D1BFE6"/>
            <stop offset="0.733" stop-color="#AC9CCD"/>
            <stop offset="1" stop-color="#8779B3"/>
        </linearGradient>
        <linearGradient id="gradient_10" x1="0.0" y1="0.0" x2="1.0" y2="1.0">
            <stop offset="0" stop-color="#FF7044"/>
            <stop offset="1" stop-color="#F92814"/>
        </linearGradient>
        <linearGradient id="gradient_11" x1="0.0" y1="0.0" x2="1.0" y2="1.0">
            <stop offset="0" stop-color="#FF7044"/>
            <stop offset="1" stop-color="#FFA425"/>
        </linearGradient>
        <linearGradient id="gradient_12" x1="0.0" y1="0.0" x2="1.0" y2="1.0">
            <stop offset="0" stop-color="#F92814"/>
            <stop offset="1" stop-color="#C1272D"/>
        </linearGradient>
        <linearGradient id="gradient_13" x1="0.0" y1="0.0" x2="1.0" y2="1.0">
            <stop offset="0" stop-color="#F9F7FC"/>
            <stop offset="1" stop-color="#F0DDFC"/>
        </linearGradient>
        <linearGradient id="gradient_14" x1="0.0" y1="0.0" x2="1.0" y2="1.0">
            <stop offset="0" stop-color="#F0DDFC"/>
            <stop offset="0.289" stop-color="#C8B7E0"/>
            <stop offset="0.592" stop-color="#A595C8"/>
            <stop offset="0.84" stop-color="#8F81B8"/>
            <stop offset="1" stop-color="#8779B3"/>
        </linearGradient>
        <linearGradient id="gradient_15" x1="0.0" y1="0.0" x2="1.0" y2="1.0">
            <stop offset="0" stop-color="#F9F7FC"/>
            <stop offset="1" stop-color="#F0DDFC"/>
        </linearGradient>
        <linearGradient id="gradient_16" x1="0.0" y1="0.0" x2="1.0" y2="1.0">
            <stop offset="0" stop-color="#FFDAAA"/>
            <stop offset="0.421" stop-color="#FFD8A8"/>
            <stop offset="0.652" stop-color="#FED1A0"/>
            <stop offset="0.836" stop-color="#FDC592"/>
            <stop offset="0.995" stop-color="#FCB47F"/>
            <stop offset="1" stop-color="#FCB37E"/>
        </linearGradient>
        <linearGradient id="gradient_17" x1="0.0" y1="0.0" x2="1.0" y2="1.0">
            <stop offset="0" stop-color="#FFDAAA"/>
            <stop offset="0.251" stop-color="#FFE7C8"/>
            <stop offset="0.554" stop-color="#FFF4E6"/>
            <stop offset="0.815" stop-color="#FFFCF8"/>
            <stop offset="1" stop-color="#FFFFFF"/>
        </linearGradient>
        <linearGradient id="gradient_18" x1="0.0" y1="0.0" x2="1.0" y2="1.0">
            <stop offset="0" stop-color="#FFDA45"/>
            <stop offset="1" stop-color="#FFA425"/>
        </linearGradient>
        <linearGradient id="gradient_19" x1="0.0" y1="0.0" x2="1.0" y2="1.0">
            <stop offset="0" stop-color="#FFDA45"/>
            <stop offset="1" stop-color="#FFA425"/>
        </linearGradient>
        <linearGradient id="gradient_20" x1="0.0" y1="0.0" x2="1.0" y2="1.0">
            <stop offset="0" stop-color="#FFDA45"/>
            <stop offset="1" stop-color="#FCEE21"/>
        </linearGradient>
        <linearGradient id="gradient_21" x1="0.0" y1="0.0" x2="1.0" y2="1.0">
            <stop offset="0" stop-color="#7E8595"/>
            <stop offset="1" stop-color="#555A66"/>
        </linearGradient>
        <linearGradient id="gradient_22" x1="0.0" y1="0.0" x2="1.0" y2="1.0">
            <stop offset="0" stop-color="#FFDA45"/>
            <stop offset="1" stop-color="#B53759"/>
        </linearGradient>
        <linearGradient id="gradient_23" x1="0.0" y1="0.0" x2="1.0" y2="1.0">
            <stop offset="0" stop-color="#F9F7FC"/>
            <stop offset="1" stop-color="#F0DDFC"/>
        </linearGradient>
        <linearGradient id="gradient_24" x1="0.0" y1="0.0" x2="1.0" y2="1.0">
            <stop offset="0" stop-color="#92F37F"/>
            <stop offset="1" stop-color="#4AB272"/>
        </linearGradient>
        <linearGradient id="gradient_25" x1="0.0" y1="0.0" x2="1.0" y2="1.0">
            <stop offset="0" stop-color="#92F37F"/>
            <stop offset="1" stop-color="#4AB272"/>
        </linearGradient>
        <linearGradient id="gradient_26" x1="0.0" y1="0.0" x2="1.0" y2="1.0">
            <stop offset="0" stop-color="#92F37F"/>
            <stop offset="1" stop-color="#4AB272"/>
        </linearGradient>
        <linearGradient id="gradient_27" x1="0.0" y1="0.0" x2="1.0" y2="1.0">
            <stop offset="0" stop-color="#92F37F"/>
            <stop offset="1" stop-color="#4AB272"/>
        </linearGradient>
    </defs>
    <g>
        <path d="M0,0h23.999v24h-23.999z" fill="none"/>
        <path d="M23.999,15.558V23.26C23.999,23.624 23.61,23.873 23.251,23.739C21.525,23.097 19.762,22.776 17.999,22.776C16.125,22.776 14.252,23.139 12.423,23.865C12.288,23.918 12.145,23.947 12,23.947C11.854,23.947 11.711,23.918 11.576,23.865C8.027,22.456 4.308,22.414 0.748,23.739C0.389,23.873 0,23.624 0,23.26V15.558C0,15.296 0.165,15.057 0.421,14.955C2.251,14.229 4.125,13.866 5.999,13.866C8.018,13.866 10.037,14.287 12,15.129C15.786,13.505 19.778,13.447 23.578,14.955C23.835,15.057 23.999,15.296 23.999,15.558Z" fill="url(#gradient_0)"/>
        <path d="M23.251,22.684C21.525,22.042 19.762,21.721 17.999,21.721C16.125,21.721 14.252,22.083 12.423,22.81C12.288,22.863 12.145,22.892 12,22.892C11.854,22.892 11.711,22.863 11.576,22.81C8.027,21.401 4.308,21.359 0.748,22.684C0.389,22.818 0,22.569 0,22.205V23.26C0,23.624 0.389,23.873 0.748,23.739C4.308,22.414 8.027,22.456 11.576,23.865C11.711,23.918 11.855,23.947 12,23.947C12.145,23.947 12.288,23.918 12.423,23.865C14.252,23.139 16.125,22.776 17.999,22.776C19.763,22.776 21.525,23.097 23.251,23.739C23.61,23.873 23.999,23.624 23.999,23.26V22.205C23.999,22.569 23.61,22.818 23.251,22.684Z" fill="url(#gradient_1)"/>
        <path d="M23.311,15.283V21.66C23.311,22.024 22.944,22.273 22.606,22.139C20.979,21.497 19.317,21.176 17.656,21.176C16.053,21.176 14.451,21.475 12.879,22.073C12.598,22.18 12.301,22.237 12,22.237C11.698,22.237 11.401,22.179 11.119,22.072C7.919,20.855 4.589,20.878 1.394,22.139C1.055,22.273 0.689,22.024 0.689,21.66V15.283C0.689,15.021 0.844,14.782 1.086,14.68C2.811,13.954 4.578,13.591 6.344,13.591C8.247,13.591 10.149,14.012 12,14.854C15.569,13.23 19.332,13.172 22.914,14.68C23.155,14.782 23.311,15.021 23.311,15.283Z" fill="url(#gradient_2)"/>
        <path d="M23.311,15.283V21.66C23.311,22.024 22.944,22.273 22.606,22.139C20.979,21.497 19.317,21.176 17.656,21.176C16.053,21.176 14.451,21.475 12.879,22.073C12.598,22.18 12.301,22.237 12,22.237C11.698,22.237 11.401,22.179 11.119,22.072C7.919,20.855 4.589,20.878 1.394,22.139C1.055,22.273 0.689,22.024 0.689,21.66V15.283C0.689,15.021 0.844,14.782 1.086,14.68C2.811,13.954 4.578,13.591 6.344,13.591C8.247,13.591 10.149,14.012 12,14.854C15.569,13.23 19.332,13.172 22.914,14.68C23.155,14.782 23.311,15.021 23.311,15.283Z" fill="url(#gradient_3)"/>
        <path d="M23.311,14.853V21.231C23.311,21.595 22.944,21.844 22.606,21.71C20.979,21.068 19.317,20.747 17.656,20.747C16.053,20.747 14.451,21.045 12.879,21.643C12.598,21.75 12.301,21.808 12,21.808C11.698,21.808 11.401,21.75 11.119,21.643C7.919,20.426 4.589,20.448 1.394,21.71C1.055,21.844 0.689,21.595 0.689,21.231V14.853C0.689,14.591 0.844,14.353 1.086,14.251C2.811,13.524 4.578,13.161 6.344,13.161C8.247,13.161 10.149,13.583 12,14.425C15.569,12.8 19.332,12.743 22.914,14.251C23.155,14.353 23.311,14.591 23.311,14.853Z" fill="url(#gradient_4)"/>
        <path d="M0.689,14.523V20.901C0.689,21.265 1.055,21.514 1.394,21.38C3.021,20.737 4.682,20.416 6.344,20.416C7.947,20.416 9.549,20.715 11.12,21.313C11.401,21.42 11.699,21.478 12,21.478C12.301,21.478 12.599,21.42 12.88,21.313C16.08,20.096 19.41,20.118 22.606,21.38C22.944,21.513 23.311,21.265 23.311,20.901V14.523C23.311,14.261 23.155,14.023 22.914,13.921C21.189,13.194 19.422,12.831 17.655,12.831C15.753,12.831 13.85,13.252 12,14.095C8.431,12.47 4.668,12.412 1.086,13.921C0.844,14.023 0.689,14.261 0.689,14.523Z" fill="url(#gradient_5)"/>
        <path d="M1.085,13.379C0.844,13.481 0.689,13.719 0.689,13.981V20.359C0.689,20.723 1.056,20.972 1.394,20.838C4.884,19.46 8.534,19.56 12,21.138C12,16.005 12,18.686 12,13.553C8.431,11.929 4.668,11.87 1.085,13.379Z" fill="url(#gradient_6)"/>
        <path d="M22.914,13.379C19.331,11.871 15.569,11.928 12,13.553V21.138C15.466,19.56 19.116,19.46 22.605,20.838C22.944,20.972 23.31,20.723 23.31,20.359C23.31,15.628 23.31,18.712 23.31,13.981C23.31,13.719 23.155,13.481 22.914,13.379Z" fill="url(#gradient_7)"/>
        <path d="M1.799,14.398C1.573,14.493 1.428,14.715 1.428,14.96V20.41C1.428,20.75 1.771,20.983 2.087,20.858C5.349,19.57 8.76,19.663 12,21.138C12,16.34 12,19.358 12,14.56C8.664,13.042 5.148,12.987 1.799,14.398Z" fill="url(#gradient_8)"/>
        <path d="M2.39,14.598C3.386,14.245 4.394,14.025 5.405,13.939C5.499,13.931 5.58,14.006 5.58,14.1V14.484C5.58,14.569 5.515,14.638 5.431,14.645C4.448,14.732 3.468,14.953 2.499,15.309C2.394,15.348 2.282,15.269 2.282,15.157V14.751C2.282,14.682 2.325,14.621 2.39,14.598Z" fill="#BEC3D2"/>
        <path d="M8.083,14.164V14.55C8.083,14.648 7.996,14.724 7.9,14.71C7.327,14.629 6.753,14.594 6.181,14.605C6.09,14.607 6.015,14.535 6.015,14.444V14.061C6.015,13.974 6.085,13.901 6.173,13.899C6.763,13.889 7.353,13.923 7.943,14.004C8.023,14.014 8.083,14.083 8.083,14.164Z" fill="#BEC3D2"/>
        <path d="M10.071,14.611V15.013C10.071,15.123 9.964,15.201 9.86,15.167C9.458,15.035 9.052,14.927 8.646,14.841C8.571,14.825 8.518,14.76 8.518,14.683V14.292C8.518,14.191 8.611,14.113 8.71,14.134C9.127,14.219 9.544,14.326 9.958,14.457C10.025,14.478 10.071,14.541 10.071,14.611Z" fill="#BEC3D2"/>
        <path d="M2.39,16.085C2.659,15.989 2.929,15.904 3.2,15.828C3.303,15.799 3.406,15.876 3.406,15.983V16.382C3.406,16.454 3.358,16.517 3.289,16.537C3.024,16.613 2.761,16.699 2.499,16.795C2.394,16.834 2.282,16.756 2.282,16.644V16.237C2.282,16.169 2.325,16.107 2.39,16.085Z" fill="#BEC3D2"/>
        <path d="M7.006,15.556V15.938C7.006,16.03 6.93,16.104 6.838,16.1C6.627,16.091 6.416,16.088 6.205,16.091C5.482,16.104 4.757,16.189 4.038,16.346C3.937,16.368 3.842,16.29 3.842,16.187V15.797C3.842,15.721 3.895,15.655 3.969,15.639C4.708,15.483 5.451,15.398 6.194,15.386C6.413,15.382 6.632,15.384 6.852,15.394C6.938,15.397 7.006,15.469 7.006,15.556Z" fill="#BEC3D2"/>
        <path d="M10.071,16.098V16.499C10.071,16.609 9.964,16.687 9.86,16.653C9.109,16.408 8.348,16.242 7.586,16.157C7.504,16.148 7.441,16.079 7.441,15.997V15.612C7.441,15.516 7.524,15.441 7.619,15.451C8.403,15.535 9.185,15.7 9.958,15.943C10.025,15.965 10.071,16.028 10.071,16.098Z" fill="#BEC3D2"/>
        <path d="M2.39,17.571C3.383,17.219 4.393,16.999 5.405,16.913C5.499,16.905 5.58,16.98 5.58,17.074V17.458C5.58,17.542 5.515,17.612 5.431,17.619C4.447,17.706 3.465,17.928 2.499,18.282C2.394,18.32 2.282,18.242 2.282,18.13V17.724C2.282,17.656 2.325,17.594 2.39,17.571Z" fill="#BEC3D2"/>
        <path d="M10.071,17.584V17.986C10.071,18.095 9.964,18.174 9.86,18.14C8.656,17.747 7.428,17.558 6.206,17.578C6.197,17.578 6.189,17.579 6.181,17.579C6.09,17.58 6.015,17.508 6.015,17.417V17.035C6.015,16.947 6.086,16.874 6.174,16.873C6.18,16.873 6.187,16.872 6.194,16.872C7.454,16.851 8.718,17.039 9.958,17.43C10.025,17.451 10.071,17.514 10.071,17.584Z" fill="#BEC3D2"/>
        <path d="M2.39,19.057C2.659,18.962 2.929,18.877 3.2,18.801C3.303,18.772 3.406,18.849 3.406,18.956V19.354C3.406,19.427 3.358,19.489 3.289,19.509C3.025,19.586 2.761,19.672 2.499,19.769C2.394,19.807 2.282,19.729 2.282,19.617V19.21C2.282,19.142 2.325,19.08 2.39,19.057Z" fill="#BEC3D2"/>
        <path d="M8.082,18.624V19.01C8.082,19.108 7.995,19.183 7.897,19.169C7.334,19.09 6.769,19.055 6.205,19.065C5.482,19.077 4.757,19.162 4.038,19.319C3.937,19.341 3.842,19.263 3.842,19.16V18.771C3.842,18.694 3.895,18.628 3.969,18.612C4.708,18.456 5.451,18.371 6.194,18.359C6.777,18.349 7.361,18.384 7.943,18.463C8.023,18.474 8.082,18.543 8.082,18.624Z" fill="#BEC3D2"/>
        <path d="M22.605,20.838C22.944,20.972 23.31,20.723 23.31,20.359C23.31,15.628 23.31,18.712 23.31,13.981C23.31,13.719 23.155,13.481 22.914,13.379C19.331,11.871 15.569,11.928 12,13.553C11.335,13.25 10.664,13.002 9.988,12.809L9.445,14.329C9.393,14.472 9.499,14.611 9.634,14.62L15.136,20.122C17.626,19.632 20.155,19.87 22.605,20.838Z" fill="url(#gradient_9)"/>
        <path d="M23.08,3.307L20.745,0.972L21.559,0.158C21.678,0.039 21.865,0.019 22.007,0.11C22.784,0.609 23.444,1.269 23.942,2.046C24.034,2.188 24.014,2.374 23.894,2.494L23.08,3.307Z" fill="url(#gradient_10)"/>
        <path d="M23.08,3.307L23.894,2.493C23.913,2.474 23.93,2.453 23.944,2.43C23.935,2.396 23.92,2.362 23.9,2.331C23.402,1.554 22.741,0.894 21.965,0.396C21.823,0.304 21.636,0.324 21.517,0.444L20.867,1.094L23.08,3.307Z" fill="url(#gradient_11)"/>
        <path d="M23.942,2.045C23.858,1.914 23.768,1.786 23.675,1.661C23.653,1.717 23.62,1.769 23.576,1.813L22.581,2.808L23.08,3.307L23.894,2.493C24.013,2.374 24.034,2.188 23.942,2.045Z" fill="url(#gradient_12)"/>
        <path d="M21.898,4.733L23.273,3.358C23.326,3.305 23.326,3.218 23.273,3.164L20.888,0.78C20.834,0.726 20.748,0.726 20.694,0.78L19.32,2.154L21.898,4.733Z" fill="url(#gradient_13)"/>
        <path d="M20.012,1.46V6.619L22.594,4.038L20.014,1.458L20.012,1.46Z" fill="url(#gradient_14)"/>
        <path d="M22.512,3.98L20.072,1.541C19.95,1.418 19.95,1.22 20.072,1.098C20.194,0.976 20.393,0.976 20.515,1.098L22.954,3.538C23.076,3.66 23.076,3.858 22.954,3.98C22.832,4.102 22.634,4.102 22.512,3.98Z" fill="url(#gradient_15)"/>
        <path d="M10.724,10.75L9.445,14.33C9.383,14.503 9.55,14.67 9.723,14.608L13.303,13.328C14.017,12.614 11.438,10.036 10.724,10.75Z" fill="url(#gradient_16)"/>
        <path d="M11.348,11.343L9.465,14.49C9.462,14.495 9.46,14.5 9.457,14.505C9.505,14.594 9.613,14.648 9.723,14.608L13.303,13.328C13.308,13.323 13.313,13.317 13.318,13.311C13.531,12.477 12.046,10.785 11.348,11.343Z" fill="url(#gradient_17)"/>
        <path d="M22.116,4.515L13.302,13.328C13.268,13.269 13.226,13.213 13.175,13.163C12.86,12.847 12.348,12.847 12.033,13.163C12.018,13.177 12.073,13.119 12.073,13.119C12.347,12.801 12.334,12.321 12.032,12.02C11.731,11.719 11.251,11.705 10.934,11.979C10.934,11.979 10.875,12.034 10.89,12.02C11.205,11.704 11.205,11.193 10.89,10.877C10.839,10.827 10.783,10.784 10.724,10.75L19.538,1.936L22.116,4.515Z" fill="url(#gradient_18)"/>
        <path d="M21.397,3.795L12.074,13.119C12.348,12.801 12.334,12.321 12.033,12.02C11.731,11.719 11.251,11.705 10.934,11.979L20.257,2.656L21.397,3.795Z" fill="url(#gradient_19)"/>
        <path d="M10.89,12.02C10.876,12.034 10.934,11.979 10.934,11.979C11.251,11.705 11.731,11.719 12.033,12.02C12.334,12.322 12.348,12.801 12.074,13.119C12.074,13.119 12.019,13.177 12.033,13.163C12.348,12.847 12.86,12.847 13.175,13.163C13.226,13.213 13.269,13.269 13.303,13.328L22.116,4.515L19.844,2.243L11.027,11.061C11.195,11.367 11.15,11.76 10.89,12.02Z" fill="url(#gradient_20)"/>
        <path d="M10.167,13.885C10.032,13.75 9.88,13.641 9.72,13.559L9.445,14.329C9.383,14.503 9.55,14.67 9.723,14.608L10.494,14.333C10.411,14.172 10.303,14.021 10.167,13.885Z" fill="url(#gradient_21)"/>
        <path d="M19.131,2.342L19.137,7.496L21.713,4.92L19.133,2.339L19.131,2.342Z" fill="url(#gradient_22)"/>
        <path d="M21.63,4.862L19.19,2.423C19.068,2.3 19.068,2.102 19.19,1.98C19.313,1.858 19.511,1.858 19.633,1.98L22.072,4.419C22.194,4.542 22.194,4.74 22.072,4.862C21.95,4.984 21.752,4.984 21.63,4.862Z" fill="url(#gradient_23)"/>
        <path d="M1.458,10.556H0.473C0.212,10.556 0,10.344 0,10.083C0,9.822 0.212,9.61 0.473,9.61H1.458C1.72,9.61 1.931,9.822 1.931,10.083C1.931,10.344 1.72,10.556 1.458,10.556Z" fill="url(#gradient_24)"/>
        <path d="M10.397,1.617V0.632C10.397,0.371 10.185,0.159 9.924,0.159C9.662,0.159 9.45,0.371 9.45,0.632V1.617C9.45,1.879 9.662,2.09 9.924,2.09C10.185,2.09 10.397,1.879 10.397,1.617Z" fill="url(#gradient_25)"/>
        <path d="M3.603,4.431L2.907,3.735C2.722,3.55 2.722,3.25 2.907,3.066C3.091,2.881 3.391,2.881 3.576,3.066L4.272,3.762C4.457,3.947 4.457,4.247 4.272,4.431C4.088,4.616 3.788,4.616 3.603,4.431Z" fill="url(#gradient_26)"/>
        <path d="M16.244,4.431L16.941,3.735C17.125,3.55 17.125,3.25 16.941,3.066C16.756,2.881 16.456,2.881 16.271,3.066L15.575,3.762C15.39,3.947 15.39,4.247 15.575,4.431C15.76,4.616 16.059,4.616 16.244,4.431Z" fill="url(#gradient_27)"/>
    </g>
</svg>