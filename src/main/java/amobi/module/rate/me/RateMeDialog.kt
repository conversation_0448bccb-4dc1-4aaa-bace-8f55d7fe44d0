package amobi.module.rate.me

import amobi.module.common.configs.PrefAssist
import amobi.module.common.configs.RconfAssist
import amobi.module.common.configs.RconfComm
import amobi.module.common.utils.FirebaseAssist
import amobi.module.common.utils.MixedUtils
import amobi.module.common.utils.onCommClick
import amobi.module.common.views.CommActivity
import amobi.module.common.views.CommDialog
import amobi.module.rate.me.databinding.DialogRateMeNewBinding
import android.content.ActivityNotFoundException
import android.content.Intent
import android.view.KeyEvent
import android.view.View
import android.view.animation.AccelerateInterpolator
import android.view.animation.Animation
import android.view.animation.AnimationUtils
import android.view.animation.OvershootInterpolator
import android.view.animation.ScaleAnimation
import android.widget.ImageView
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.content.res.AppCompatResources
import androidx.core.content.ContextCompat
import androidx.core.net.toUri
import com.bumptech.glide.Glide

class RateMeDialog(
    activity: CommActivity,
    where: String,
    parentTag: String,
    backgroundColor: Int? = null,
    backgroundImage: Int? = null,
    textColor: Int? = null,
    bgDimAmount: Float? = null,
    actionWhenDismissed: (Boolean, Int) -> Unit,
) : CommDialog(activity) {
    companion object {
        const val TAG_DIALOG: String = "RateMeDialog"
    }

    var rating = 0
    var imgvStars = arrayOf<ImageView>()

    init {
        FirebaseAssist.instance.collectPropertyOpenRateMeDialogCounter()

        val binding = DialogRateMeNewBinding.inflate(layoutInflater)
        this.setContentView(binding.root)

        setupDialogWindow(bgDimAmount)

        val imgvRateLogo = binding.imageViewRateLogo
        val bttnOk = binding.bttnOk
        val txtvTitle = binding.txtvTitle
        txtvTitle.visibility = View.GONE
        val txtvContent1 = binding.txtvContent1
        val txtvContent2 = binding.txtvContent2
        bttnOk.visibility = View.GONE
        imgvRateLogo.setImageResource(R.drawable.svg_emoji_smile)

        backgroundColor?.let {
            binding.llytRateMeDialog.setBackgroundColor(ContextCompat.getColor(context, it))
        }
        backgroundImage?.let {
            binding.imgvBackground.visibility = View.VISIBLE

            Glide
                .with(context)
                .load(it)
                .centerInside()
                .into(binding.imgvBackground)
        }

        textColor?.let {
            val color = AppCompatResources.getColorStateList(context, it)
            txtvTitle.setTextColor(color)
            txtvContent1.setTextColor(color)
            txtvContent2.setTextColor(color)
            binding.imgvClose.setColorFilter(ContextCompat.getColor(context, it))
        }

        imgvStars =
            arrayOf(
                binding.imageViewStar1,
                binding.imageViewStar2,
                binding.imageViewStar3,
                binding.imageViewStar4,
                binding.imageViewStar5,
            )

        for (starIndex in imgvStars.indices) {
            imgvStars[starIndex].setImageResource(R.drawable.svg_rating_filled)
            animateScaleDownWithDelay(imgvStars[starIndex], 100L * starIndex)
            imgvStars[starIndex].onCommClick(parentTag, TAG_DIALOG, "StarIndex$starIndex") { v ->
                when (v) {
                    imgvStars[0] -> rating = 1
                    imgvStars[1] -> rating = 2
                    imgvStars[2] -> rating = 3
                    imgvStars[3] -> rating = 4
                    imgvStars[4] -> rating = 5
                }
                FirebaseAssist.instance.logRateBttnClicked(where, "$rating*")

                bttnOk.visibility = View.VISIBLE
                txtvTitle.visibility = View.VISIBLE
                txtvContent1.visibility = View.VISIBLE
                if (rating >= 4) {
                    txtvContent2.visibility = View.GONE
                    if (rating < 5) {
                        bttnOk.setText(R.string.rate_me_rate_us)
                    } else {
                        bttnOk.setText(R.string.rate_me_rate_us_on_google_play)
                    }
                    imgvRateLogo.setImageResource(R.drawable.svg_emoji_star_struck)
                    if (rating >= 5) {
                        txtvTitle.setText(R.string.rate_me_we_love_it)
                    } else {
                        txtvTitle.setText(R.string.rate_me_thats_great)
                    }
                    txtvContent1.setText(R.string.rate_me_thank_you_for_your_feedback)
                } else {
                    txtvContent2.visibility = View.VISIBLE
                    bttnOk.setText(R.string.rate_me_share_feedback)
                    imgvRateLogo.setImageResource(R.drawable.svg_emoji_with_tear)
                    txtvTitle.setText(R.string.rate_me_oh_no)
                    txtvContent1.setText(R.string.rate_me_we_are_sorry_to_know_your_experience)
                    txtvContent2.setText(R.string.rate_me_please_leave_us_some_feedback)
                }

                for (i in imgvStars.indices) {
                    if (rating - 1 < i)
                        imgvStars[i].setImageResource(R.drawable.svg_rating_empty)
                    else
                        imgvStars[i].setImageResource(R.drawable.svg_rating_filled)
                }
            }
        }

        if (RconfAssist.getBoolean(RconfComm.COMM_RATE_ME_SHOW_CLOSE_BUTTON)) {
            binding.llytClose.onCommClick(parentTag, TAG_DIALOG, "CloseButton") {
                FirebaseAssist.instance.logRateBttnClicked(where, "CloseD")
                this.dismiss()
            }
        } else {
            binding.llytClose.visibility = View.GONE
        }

        var isBttnOkClicked = false
        bttnOk.onCommClick(parentTag, TAG_DIALOG, "RateButton") {
            when (rating) {
                4 -> FirebaseAssist.instance.logRateBttnClicked(where, "GooD_$rating*")
                5 -> FirebaseAssist.instance.logRateBttnClicked(where, "RateUs_$rating*")
                else -> FirebaseAssist.instance.logRateBttnClicked(where, "FeeDBacK_$rating*")
            }

            if (rating <= 0f) return@onCommClick
            isBttnOkClicked = true

            if (rating < 4) {
                this.dismiss()
                return@onCommClick
            } else if (rating < 5) {
                MixedUtils.showToastImmediately(context, context.getString(R.string.rate_me_thank_you_for_your_rating))
                this.dismiss()
                return@onCommClick
            }

            this.dismiss()
            rateAppsIntent(activity)
        }

        this.setOnKeyListener { _, keyCode, event ->
            if (keyCode == KeyEvent.KEYCODE_BACK && event.action == KeyEvent.ACTION_UP) {
                FirebaseAssist.instance.logRateBttnClicked(where, "CloseDByBack")
                this.dismiss()
            }
            true
        }

        setOnDismissListener {
            PrefAssist.setInt(RateConst.RATE_ME_STAR_CHOSE, rating)
            PrefAssist.setBoolean(RateConst.RATE_ME_OK_CLICKED, isBttnOkClicked)

            PrefAssist.setLong(RateConst.RATE_ME_STAR_CHOSE_RECORD_TIME, System.currentTimeMillis())
            actionWhenDismissed(isBttnOkClicked, rating)
        }
    }

    private fun animateScaleDownWithDelay(
        view: ImageView,
        delay: Long,
    ) {
        val anim: Animation =
            ScaleAnimation(
                1f,
                0.5f,
                1f,
                0.5f,
                Animation.RELATIVE_TO_SELF,
                0.5f, // Pivot point of X scaling
                Animation.RELATIVE_TO_SELF,
                0.5f, // Pivot point of Y scaling
            )
        anim.fillAfter = true // Needed to keep the result of the animation
        anim.duration = 300
        anim.startOffset = delay
        anim.interpolator = AccelerateInterpolator()
        anim.setAnimationListener(
            object : Animation.AnimationListener {
                override fun onAnimationStart(animation: Animation) {}

                override fun onAnimationEnd(animation: Animation) {
                    animateScaleUpWithDelay(view)
                }

                override fun onAnimationRepeat(animation: Animation) {}
            },
        )
        view.startAnimation(anim)
    }

    private fun animateScaleUpWithDelay(view: ImageView) {
        val anim: Animation =
            ScaleAnimation(
                0.5f,
                1f,
                0.5f,
                1f,
                Animation.RELATIVE_TO_SELF,
                0.5f, // Pivot point of X scaling
                Animation.RELATIVE_TO_SELF,
                0.5f, // Pivot point of Y scaling
            )
        anim.fillAfter = true // Needed to keep the result of the animation
        anim.duration = 300
        anim.interpolator = OvershootInterpolator(3f)
        anim.setAnimationListener(
            object : Animation.AnimationListener {
                override fun onAnimationStart(animation: Animation) {}

                override fun onAnimationEnd(animation: Animation) {
                    if (view.id == R.id.imageViewStar5) {
                        val animShake = AnimationUtils.loadAnimation(context, R.anim.anim_shake)
                        animShake.setAnimationListener(
                            object : Animation.AnimationListener {
                                override fun onAnimationStart(animation: Animation) {}

                                override fun onAnimationEnd(animation: Animation) {
                                    if (rating == 0) {
                                        for (imgv in imgvStars) {
                                            imgv.setImageResource(R.drawable.svg_rating_empty)
                                        }
                                    }
                                }

                                override fun onAnimationRepeat(animation: Animation) {}
                            },
                        )
                        view.startAnimation(animShake)
                    }
                }

                override fun onAnimationRepeat(animation: Animation) {}
            },
        )
        view.startAnimation(anim)
    }

    private fun rateAppsIntent(activity: AppCompatActivity) {
        val intent = Intent(Intent.ACTION_VIEW)
        try {
            intent.data = ("market://details?id=" + activity.packageName).toUri()
            activity.startActivity(intent)
        } catch (e: ActivityNotFoundException) {
            try {
                intent.data =
                    ("https://play.google.com/store/apps/details?id=" + activity.packageName).toUri()
                activity.startActivity(intent)
            } catch (_: Exception) {
            }
        }
    }
}
