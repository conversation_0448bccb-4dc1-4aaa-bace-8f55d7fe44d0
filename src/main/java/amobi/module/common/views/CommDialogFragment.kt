package amobi.module.common.views

import amobi.module.common.configs.CommFigs
import amobi.module.common.utils.DebugLogCustom
import android.graphics.Color
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.graphics.drawable.toDrawable
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import androidx.viewbinding.ViewBinding
import java.util.concurrent.atomic.AtomicBoolean

abstract class CommDialogFragment<VB : ViewBinding>(
    private val bindingInflate: BindingInflate<VB>,
) : DialogFragment() {
    protected var rootBinding: VB? = null
    protected val binding get() = rootBinding!!

    private var mBlurEngine: BlurDialogComponent? = null

    fun enableBlurView(
        blurRadius: Int = 4,
        downScaleFactor: Float = 6f,
    ) {
        if (CommFigs.IS_WEAK_DEVICE) return
        val activity = activity ?: return
        mBlurEngine =
            BlurDialogComponent(activity).apply {
                setBlurRadius(blurRadius)
                setDownScaleFactor(downScaleFactor)
                debug(false)
                setBlurActionBar(false)
                setUseRenderScript(false)
            }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        rootBinding = bindingInflate.invoke(inflater, container, false)
        return binding.root
    }

    override fun onResume() {
        super.onResume()
        mBlurEngine?.onResume(retainInstance)
    }

    fun setupDialogWindow(
        dimAmount: Float? = null,
        gravity: Int = Gravity.CENTER,
    ) {
        dialog?.window?.let {
            it.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
            it.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
            it.setGravity(gravity)
            if (dimAmount != null)
                it.setDimAmount(dimAmount)
        }

        dialog?.setOnDismissListener {
            dismissAllowingStateLoss()
        }
    }

    private var isOnDismissCalled = AtomicBoolean(false)

    override fun dismiss() {
        super.dismiss()
        if (isOnDismissCalled.getAndSet(true)) return
        onDismiss()
        mBlurEngine?.onDismiss()
    }

    override fun dismissAllowingStateLoss() {
        super.dismissAllowingStateLoss()
        if (isOnDismissCalled.getAndSet(true)) return
        onDismiss()
        mBlurEngine?.onDismiss()
    }

    abstract fun onDismiss()

    var isDestroyed: Boolean = false

    override fun onDestroy() {
        isDestroyed = true
        dismissAllowingStateLoss()
        super.onDestroy()
        mBlurEngine?.onDetach()
    }

    override fun onDestroyView() {
        dialog?.setDismissMessage(null)
        super.onDestroyView()
    }

    open fun isSafe(): Boolean = !isNotSafe()

    open fun isNotSafe(): Boolean {
        if (!isSafeActivity()) return true
        return (this.isRemoving || this.activity == null || this.isDetached || !this.isAdded || this.isDestroyed || this.view == null)
    }

    open fun isSafeActivity(): Boolean {
        val activity = this.activity ?: return false
        return !(activity.isFinishing || activity.isDestroyed)
    }

    override fun show(
        manager: FragmentManager,
        tag: String?,
    ) {
        try {
            super.show(manager, tag)
        } catch (e: IllegalStateException) {
            DebugLogCustom.logd("Exception $e")
        }
    }
}
