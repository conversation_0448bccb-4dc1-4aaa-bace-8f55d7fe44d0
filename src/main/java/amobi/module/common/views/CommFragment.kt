package amobi.module.common.views

import androidx.fragment.app.Fragment

abstract class CommFragment : Fragment() {
    var isDestroyed: Boolean = false

    override fun onDestroy() {
        isDestroyed = true
        super.onDestroy()
    }

    open fun onRequestPermissionsDenied(
        requestCode: Int,
        permissions: Array<String?>,
    ) {}

    open fun isSafe(): Boolean = !isNotSafe()

    open fun isNotSafe(): Boolean {
        if (!isSafeActivity()) return true
        return (this.isRemoving || this.activity == null || this.isDetached || !this.isAdded || this.isDestroyed || this.view == null)
    }

    open fun isSafeActivity(): Boolean {
        val activity = this.activity ?: return false
        return !(activity.isFinishing || activity.isDestroyed)
    }
}
