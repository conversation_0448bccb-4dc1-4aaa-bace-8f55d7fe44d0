// Copyright 2019 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
package amobi.module.common.advertisements.native_ad

import amobi.module.common.R
import android.content.Context
import android.graphics.drawable.Drawable
import android.text.TextUtils
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.Button
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.RatingBar
import android.widget.TextView
import com.google.android.gms.ads.nativead.MediaView
import com.google.android.gms.ads.nativead.NativeAd
import com.google.android.gms.ads.nativead.NativeAdView

/**
 * Base class for a template view. *
 */
class NativeTemplateView : FrameLayout {
    private var templateType = 0
    var isAdChoiceTop = false
        private set
    var isShowAdIcon = false
    private var styles: NativeTemplateStyle? = null
    private var nativeAd: NativeAd? = null
    private var nativeAdView: NativeAdView? = null
    private var primaryView: TextView? = null
    private var adTagView: View? = null
    private var secondaryView: TextView? = null
    private var ratingBar: RatingBar? = null
    private var tertiaryView: TextView? = null
    private var iconView: ImageView? = null
    private var mediaView: MediaView? = null
    private var callToActionView: Button? = null
    private var background: View? = null

    constructor(context: Context?) : super(context!!)
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        initView(context, attrs)
    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr) {
        initView(context, attrs)
    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int, defStyleRes: Int) : super(
        context,
        attrs,
        defStyleAttr,
        defStyleRes,
    ) {
        initView(context, attrs)
    }

    fun setStyles(styles: NativeTemplateStyle) {
        this.styles = styles
        applyStyles()
    }

    private fun applyStyles() {
        val mainBackground: Drawable? = styles?.mainBackgroundColor
        if (mainBackground != null) {
            background?.background = mainBackground
            primaryView?.background = mainBackground
            secondaryView?.background = mainBackground
            tertiaryView?.background = mainBackground
        }
        val primary = styles?.primaryTextTypeface
        if (primary != null && primaryView != null) {
            primaryView?.typeface = primary
        }
        val secondary = styles?.secondaryTextTypeface
        if (secondary != null) {
            secondaryView?.typeface = secondary
        }
        val tertiary = styles?.tertiaryTextTypeface
        if (tertiary != null) {
            tertiaryView?.typeface = tertiary
        }
        val ctaTypeface = styles?.callToActionTextTypeface
        if (ctaTypeface != null) {
            callToActionView?.typeface = ctaTypeface
        }
        if (styles?.primaryTextTypefaceColor != null) {
            primaryView?.setTextColor(styles!!.primaryTextTypefaceColor!!)
        }
        if (styles?.secondaryTextTypefaceColor != null) {
            secondaryView?.setTextColor(styles!!.secondaryTextTypefaceColor!!)
        }
        if (styles?.tertiaryTextTypefaceColor != null) {
            tertiaryView?.setTextColor(styles!!.tertiaryTextTypefaceColor!!)
        }
        if (styles?.callToActionTypefaceColor != null) {
            callToActionView?.setTextColor(styles!!.callToActionTypefaceColor!!)
        }
        val ctaTextSize = styles!!.callToActionTextSize
        if (ctaTextSize > 0) {
            callToActionView?.textSize = ctaTextSize
        }
        val primaryTextSize = styles!!.primaryTextSize
        if (primaryTextSize > 0) {
            primaryView?.textSize = primaryTextSize
        }
        val secondaryTextSize = styles!!.secondaryTextSize
        if (secondaryTextSize > 0) {
            secondaryView?.textSize = secondaryTextSize
        }
        val tertiaryTextSize = styles!!.tertiaryTextSize
        if (tertiaryTextSize > 0) {
            tertiaryView?.textSize = tertiaryTextSize
        }
        val ctaBackground: Drawable? = styles?.callToActionBackgroundColor
        if (ctaBackground != null) {
            callToActionView?.background = ctaBackground
        }
        val primaryBackground: Drawable? = styles?.primaryTextBackgroundColor
        if (primaryBackground != null) {
            primaryView?.background = primaryBackground
        }
        val secondaryBackground: Drawable? = styles?.secondaryTextBackgroundColor
        if (secondaryBackground != null) {
            secondaryView?.background = secondaryBackground
        }
        val tertiaryBackground: Drawable? = styles?.tertiaryTextBackgroundColor
        if (tertiaryBackground != null) {
            tertiaryView?.background = tertiaryBackground
        }
        invalidate()
        requestLayout()
    }

    private fun adHasOnlyStore(nativeAd: NativeAd): Boolean {
        val store = nativeAd.store
        val advertiser = nativeAd.advertiser
        return !TextUtils.isEmpty(store) && TextUtils.isEmpty(advertiser)
    }

    fun setNativeAd(nativeAd: NativeAd) {
        this.nativeAd = nativeAd
        val store = nativeAd.store
        val advertiser = nativeAd.advertiser
        val headline = nativeAd.headline
        val body = nativeAd.body
        val cta = nativeAd.callToAction
        val starRating = nativeAd.starRating
        val icon = nativeAd.icon
        val secondaryText: String?
        nativeAdView?.callToActionView = callToActionView
        nativeAdView?.headlineView = primaryView
        nativeAdView?.mediaView = mediaView
        secondaryView?.visibility = VISIBLE
        if (adHasOnlyStore(nativeAd)) {
            nativeAdView?.storeView = secondaryView
            secondaryText = store
        } else if (!TextUtils.isEmpty(advertiser)) {
            nativeAdView?.advertiserView = secondaryView
            secondaryText = advertiser
        } else {
            secondaryText = ""
        }
        primaryView?.text = headline
        callToActionView?.text = cta

        //  Set the secondary view to be the star rating if available.
        if (starRating != null && starRating > 0) {
            secondaryView?.visibility = GONE
            ratingBar?.visibility = VISIBLE
            ratingBar?.rating = starRating.toFloat()
            nativeAdView?.starRatingView = ratingBar
        } else {
            secondaryView?.text = secondaryText
            secondaryView?.visibility = VISIBLE
            ratingBar?.visibility = GONE
        }
        if (icon != null && (isShowAdIcon || nativeAd.store != null)) {
            iconView?.visibility = VISIBLE
            iconView?.setImageDrawable(icon.drawable)
        } else {
            iconView?.visibility = GONE
        }
        if (tertiaryView != null) {
            tertiaryView?.text = body
            nativeAdView?.bodyView = tertiaryView
        }

        adTagView?.visibility = View.VISIBLE
        nativeAdView?.setNativeAd(nativeAd)
    }

    /**
     * To prevent memory leaks, make sure to destroy your ad when you don't need it anymore. This
     * method does not destroy the template view.
     * https://developers.google.com/admob/android/native-unified#destroy_ad
     */
    fun destroyNativeAd() {
        nativeAd?.destroy()
    }

    private fun initView(
        context: Context,
        attributeSet: AttributeSet?,
    ) {
        val attributes = context.theme.obtainStyledAttributes(attributeSet, R.styleable.TemplateView, 0, 0)
        try {
            templateType = attributes.getResourceId(R.styleable.TemplateView_gnt_template_type, R.layout.comm_native_ad_large_cta_bottom)
            isAdChoiceTop = attributes.getBoolean(R.styleable.TemplateView_gnt_ad_choice_placement_top, true)
            isShowAdIcon = attributes.getBoolean(R.styleable.TemplateView_gnt_template_ad_icon, true)
        } finally {
            attributes.recycle()
        }
        val inflater = context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
        inflater.inflate(templateType, this)
    }

    public override fun onFinishInflate() {
        super.onFinishInflate()
        nativeAdView = findViewById(R.id.native_ad_view)
        primaryView = findViewById(R.id.native_ad_text_primary)
        adTagView = findViewById(R.id.native_ad_tag_view)
        adTagView?.visibility = View.GONE
        secondaryView = findViewById(R.id.native_ad_text_secondary)
        tertiaryView = findViewById(R.id.native_ad_text_body)
        ratingBar = findViewById(R.id.native_ad_rating_bar)
        ratingBar?.isEnabled = false
        callToActionView = findViewById(R.id.native_ad_text_cta)
        iconView = findViewById(R.id.native_ad_text_icon)
        mediaView = findViewById(R.id.native_ad_media_view)
        background = findViewById(R.id.native_ad_background)
    }

    fun isNativeAdShowing(): Boolean = nativeAd != null
}
