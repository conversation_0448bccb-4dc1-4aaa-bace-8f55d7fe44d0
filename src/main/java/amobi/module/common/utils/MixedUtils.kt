package amobi.module.common.utils

import amobi.module.common.CommApplication
import amobi.module.common.configs.CommFigs
import amobi.module.common.views.CommActivity
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Context.VIBRATOR_SERVICE
import android.content.pm.PackageManager
import android.content.res.Resources
import android.location.LocationManager
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.os.Build
import android.os.VibrationEffect
import android.os.Vibrator
import android.os.VibratorManager
import android.util.TypedValue
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.webkit.CookieManager
import android.widget.EditText
import android.widget.Toast
import androidx.annotation.StringRes
import androidx.core.text.TextUtilsCompat
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import java.util.Locale
import java.util.Random

object MixedUtils {
    fun isR2LByLocale(): Boolean {
        if (CommFigs.IS_TEST_RTL) return true
        return TextUtilsCompat.getLayoutDirectionFromLocale(Locale.getDefault()) != ViewCompat.LAYOUT_DIRECTION_LTR
    }

    fun isR2LByLanguage(languageCode: String): Int {
        val languageCodeSplit = languageCode.split("-")
        return when (languageCodeSplit[0]) {
            "ar" -> View.LAYOUT_DIRECTION_RTL
            "fa" -> View.LAYOUT_DIRECTION_RTL
            "he" -> View.LAYOUT_DIRECTION_RTL
            "iw" -> View.LAYOUT_DIRECTION_RTL
            "ur" -> View.LAYOUT_DIRECTION_RTL
            "yi" -> View.LAYOUT_DIRECTION_RTL
            else -> View.LAYOUT_DIRECTION_LTR
        }
    }

    fun getRandomGeneratedLetters(len: Int): String {
        val possible = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
        val builder = StringBuilder()
        for (i in 0 until len) {
            val random = Random()
            builder.append(possible[random.nextInt(possible.length)])
        }
        return builder.toString()
    }

    fun showKeyboard(
        activity: Activity?,
        editText: EditText?,
    ) {
        if (activity == null || activity.isDestroyed || editText == null) return
        val imm = activity.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        imm.showSoftInput(editText, InputMethodManager.SHOW_IMPLICIT)
    }

    fun hideKeyboard(activity: Activity?) {
        if (activity == null || activity.isDestroyed) return
        val view = activity.currentFocus ?: return
        val imm = activity.getSystemService(Activity.INPUT_METHOD_SERVICE) as InputMethodManager
        imm.hideSoftInputFromWindow(view.windowToken, 0)
    }

    fun isInstallFromUpdate(context: Context): Boolean =
        try {
            val firstInstallTime =
                context.packageManager.getPackageInfo(context.packageName, 0).firstInstallTime
            val lastUpdateTime =
                context.packageManager.getPackageInfo(context.packageName, 0).lastUpdateTime
            firstInstallTime != lastUpdateTime
        } catch (e: PackageManager.NameNotFoundException) {
            e.printStackTrace()
            false
        }

    const val TIMER_BETWEEN_CLICK = 300
    const val TIMER_BETWEEN_CLICK_MEDIUM = 650
    const val TIMER_BETWEEN_CLICK_LONG = 950
    const val TIMER_BETWEEN_CLICK_SHORT = 150
    private var lastValidClickTime = 0L

    fun isNotAllowClick(customTime: Int = TIMER_BETWEEN_CLICK): Boolean = !isAllowClick(customTime)

    private fun isAllowClick(customTime: Int = TIMER_BETWEEN_CLICK): Boolean =
        if (System.currentTimeMillis() - lastValidClickTime > customTime) {
            lastValidClickTime = System.currentTimeMillis()
            true
        } else {
            false
        }

    fun isNotAllowClickShort(): Boolean = !isAllowClick(TIMER_BETWEEN_CLICK_SHORT)

    fun isNotAllowClickLong(): Boolean = !isAllowClick(TIMER_BETWEEN_CLICK_LONG)

    fun isNetworkConnected(contextNullable: Context? = null): Boolean {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.M) {
            return true
        }
        val context = contextNullable ?: CommApplication.appContext
        val connectivityManager =
            context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val nw = connectivityManager.activeNetwork ?: return false
        val actNw = connectivityManager.getNetworkCapabilities(nw) ?: return false
        return when {
            actNw.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> true
            actNw.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> true
            // for other device how are able to connect with Ethernet
            actNw.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) -> true
            // for check internet over Bluetooth
            actNw.hasTransport(NetworkCapabilities.TRANSPORT_BLUETOOTH) -> true
            else -> false
        }
    }

    fun isLocationGPSEnabled(contextNullable: Context?): Boolean {
        val context = contextNullable ?: CommApplication.appContext
        val locationManager = context.getSystemService(Context.LOCATION_SERVICE) as LocationManager
        return locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER)
    }

    private var lastToast: Toast? = null

    fun showToast(
        contextNullable: Context?,
        @StringRes stringId: Int,
        toastLength: Int = Toast.LENGTH_SHORT,
    ) {
        val context = contextNullable ?: CommApplication.appContext
        val message = context.getString(stringId)
        showToast(context, message, toastLength)
    }

    fun showToast(
        @StringRes stringId: Int,
        toastLength: Int = Toast.LENGTH_SHORT,
    ) {
        showToast(CommApplication.appContext, stringId, toastLength)
    }

    fun showToast(
        message: String,
        toastLength: Int = Toast.LENGTH_SHORT,
    ) {
        showToast(CommApplication.appContext, message, toastLength)
    }

    fun showToastImmediately(
        contextNullable: Context?,
        message: String,
        toastLength: Int = Toast.LENGTH_SHORT,
    ) {
        if (message.isEmpty()) return
        val context = contextNullable ?: CommApplication.appContext
        lastToast?.cancel()
        showToast(context, message, toastLength)
    }

    fun showToast(
        contextNullable: Context?,
        message: String,
        toastLength: Int = Toast.LENGTH_SHORT,
    ) {
        if (message.isEmpty()) return
        val context = contextNullable ?: CommApplication.appContext
        try {
            val lastToast = Toast.makeText(context, message, toastLength)
            lastToast.setGravity(17, 0, 230)
            lastToast.show()
        } catch (e: Exception) {
            DebugLogCustom.loge(" fatal $e")
        }
    }

    private var isWebViewEnabled: Boolean? = null

    fun webViewEnabled(): Boolean {
        if (isWebViewEnabled == null) {
            isWebViewEnabled =
                try {
                    CookieManager.getInstance()
                    true
                } catch (e: Exception) {
                    false
                }
        }
        return isWebViewEnabled != false
    }

    fun vibrateTickPhone(contextNullable: Context? = null) {
        val context = contextNullable ?: CommApplication.appContext
        val vibrator =
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                try {
                    @SuppressLint("WrongConstant")
                    val vibratorManager =
                        context.getSystemService(Context.VIBRATOR_MANAGER_SERVICE) as VibratorManager
                    vibratorManager.defaultVibrator
                } catch (e: Exception) {
                    @Suppress("DEPRECATION")
                    context.getSystemService(VIBRATOR_SERVICE) as Vibrator
                }
            } else {
                @Suppress("DEPRECATION")
                context.getSystemService(VIBRATOR_SERVICE) as Vibrator
            }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            vibrator.vibrate(VibrationEffect.createPredefined(VibrationEffect.EFFECT_TICK))
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            vibrator.vibrate(VibrationEffect.createOneShot(10, VibrationEffect.DEFAULT_AMPLITUDE))
        } else {
            @Suppress("DEPRECATION")
            vibrator.vibrate(10)
        }
    }

    fun currentTimeSeconds(): Long = System.currentTimeMillis() / 1000

    fun currentTimeMinutes(): Long = System.currentTimeMillis() / 1000 / 60

    fun isDeviceHeightSmall(contextNullable: Context? = null): Boolean {
        val context = contextNullable ?: CommApplication.appContext
        val displayMetrics = context.resources.displayMetrics
        val deviceHeightDp = (displayMetrics.heightPixels / displayMetrics.density)
        return deviceHeightDp < 800
    }

    fun dpToPx(
        contextNullable: Context?,
        dp: Float,
    ): Int {
        val context = contextNullable ?: CommApplication.appContext
        return TypedValue
            .applyDimension(
                TypedValue.COMPLEX_UNIT_DIP,
                dp,
                context.resources.displayMetrics,
            ).toInt()
    }

    /**
     * Value of dp to value of px.
     *
     * @param dpValue The value of dp.
     * @return value of px
     */
    fun dp2px(dpValue: Float): Int {
        val scale = Resources.getSystem().displayMetrics.density
        return (dpValue * scale + 0.5f).toInt()
    }

    /**
     * Value of px to value of dp.
     *
     * @param pxValue The value of px.
     * @return value of dp
     */
    fun px2dp(pxValue: Float): Int {
        val scale = Resources.getSystem().displayMetrics.density
        return (pxValue / scale + 0.5f).toInt()
    }

    fun currentDayOfYear(): Int =
        java.util.Calendar
            .getInstance()
            .get(java.util.Calendar.DAY_OF_YEAR)

    fun currentVersionCode(): Int =
        try {
            val context = CommApplication.appContext
            @Suppress("DEPRECATION")
            context.packageManager.getPackageInfo(context.packageName, 0).versionCode
        } catch (e: PackageManager.NameNotFoundException) {
            -1
        }

    /**
     * Check if gesture navigation mode is active.
     * In gesture navigation mode, the standard navigation bar is hidden and replaced with a gesture area with a handle.
     *
     * To detect if the device is in gesture navigation mode, both conditions should be met:
     * 1. At least one system gesture horizontal inset is greater than 0.
     * 2. Navigation bar horizontal insets are equal to 0.
     */
    fun isInGestureNavigationMode(
        activity: CommActivity,
        onCompleteListener: ((Boolean) -> Unit)? = null,
    ) {
        if (activity.isNotSafe()) return

        fun isGestureNavMode(insets: WindowInsetsCompat): Boolean {
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.Q) {
                return false
            }
            val systemGesturesInsets = insets.getInsetsIgnoringVisibility(WindowInsetsCompat.Type.systemGestures())
            val navigationBarsInsets = insets.getInsetsIgnoringVisibility(WindowInsetsCompat.Type.navigationBars())

            val hasSystemGestureHorizontalInset =
                systemGesturesInsets.left > 0 || systemGesturesInsets.right > 0
            val hasNavigationBarHorizontalInset =
                navigationBarsInsets.left > 0 || navigationBarsInsets.right > 0

            if (!hasNavigationBarHorizontalInset && !hasSystemGestureHorizontalInset) {
                try {
                    val resources = activity.resources
                    val resourceId =
                        resources.getIdentifier("config_navBarInteractionMode", "integer", "android")
                    if (resourceId > 0 && resources.getInteger(resourceId) == 2)
                        return true
                } catch (e: Exception) {
                }
            }

            return hasSystemGestureHorizontalInset && !hasNavigationBarHorizontalInset
        }

        ViewCompat.setOnApplyWindowInsetsListener(activity.window.decorView) { view, insets ->
            onCompleteListener?.invoke(isGestureNavMode(insets))
            return@setOnApplyWindowInsetsListener insets
        }
    }
}
