package amobi.module.common.utils

private fun String.findFirstSince(position: Int, test: (Char) -> <PERSON><PERSON><PERSON>): Int {
    for (i in position until length) {
        if (test(this[i])) return i
    }
    return length
}

fun String.capitaliseEachWord(): String {
    return sequence {
        var startIndex = 0
        while (startIndex < <EMAIL>) {
            // Skip whitespaces
            startIndex = <EMAIL>(startIndex) { !it.isWhitespace() }
            if (startIndex >= <EMAIL>) break
            
            // Find end of word
            val endIndex = <EMAIL>(startIndex) { it.isWhitespace() }
            yield(<EMAIL>(startIndex, endIndex))
            startIndex = endIndex
        }
    }.joinToString(" ") { it.replaceFirstChar(Char::uppercaseChar) }
}
