package amobi.module.common.utils

import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch

fun LifecycleOwner.launchWhenStarted(oneTimeWork: suspend CoroutineScope.() -> Unit): Job {
    @Suppress("DEPRECATION")
    return this.lifecycleScope.launchWhenStarted {
        oneTimeWork()
    }
}

fun LifecycleOwner.launchWhenResumed(oneTimeWork: suspend CoroutineScope.() -> Unit): Job {
    @Suppress("DEPRECATION")
    return this.lifecycleScope.launchWhenResumed {
        oneTimeWork()
    }
}

fun LifecycleOwner.repeatOnceWhenStarted(oneTimeWork: suspend CoroutineScope.() -> Unit): Job {
    return this.lifecycleScope.launch {
        var isComplete = false
        <EMAIL>(Lifecycle.State.STARTED) {
            if (isComplete)
                return@repeatOnLifecycle
            oneTimeWork()
            isComplete = true
        }
    }
}

fun LifecycleOwner.repeatOnceWhenResumed(oneTimeWork: suspend CoroutineScope.() -> Unit): Job {
    return this.lifecycleScope.launch {
        var isComplete = false
        <EMAIL>(Lifecycle.State.RESUMED) {
            if (isComplete)
                return@repeatOnLifecycle
            oneTimeWork()
            isComplete = true
        }
    }
}
