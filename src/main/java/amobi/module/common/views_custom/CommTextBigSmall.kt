package amobi.module.common.views_custom

import amobi.module.common.databinding.CommTextBigSmallBinding
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.res.ResourcesCompat
import androidx.core.content.withStyledAttributes

class CommTextBigSmall : LinearLayout {
    constructor(context: Context) : super(context)

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        initView(context, attrs)
    }

    constructor(context: Context, attrs: AttributeSet?, defStyle: Int) : super(context, attrs, defStyle) {
        initView(context, attrs)
    }

    private lateinit var binding: CommTextBigSmallBinding

    private fun initView(
        context: Context,
        attributeSet: AttributeSet?,
    ) {
        binding =
            CommTextBigSmallBinding.inflate(LayoutInflater.from(context), this, true).apply {
                context.withStyledAttributes(
                    attributeSet,
                    amobi.module.common.R.styleable.CommTextBigSmall
                ) {
                    txtvBig.text =
                        getString(amobi.module.common.R.styleable.CommTextBigSmall_textBigStr)
                    txtvSmall.text =
                        getString(amobi.module.common.R.styleable.CommTextBigSmall_textSmallStr)

                    getResourceId(
                        amobi.module.common.R.styleable.CommTextBigSmall_textBigFont,
                        -1
                    ).takeIf { it != -1 }?.also {
                        txtvBig.typeface = ResourcesCompat.getFont(context, it)
                        txtvSmall.typeface = ResourcesCompat.getFont(context, it)
                    }
                    getResourceId(
                        amobi.module.common.R.styleable.CommTextBigSmall_textSmallFont,
                        -1
                    ).takeIf { it != -1 }?.also {
                        txtvSmall.typeface = ResourcesCompat.getFont(context, it)
                    }

                    getResourceId(
                        amobi.module.common.R.styleable.CommTextBigSmall_textBigColor,
                        -1
                    ).takeIf { it != -1 }?.also {
                        txtvBig.setTextColor(it)
                        txtvSmall.setTextColor(it)
                    }
                    getResourceId(
                        amobi.module.common.R.styleable.CommTextBigSmall_textSmallColor,
                        -1
                    ).takeIf { it != -1 }?.also {
                        txtvSmall.setTextColor(it)
                    }

                    // handle text size
                    getDimensionPixelSize(
                        amobi.module.common.R.styleable.CommTextBigSmall_textBigSize,
                        -1
                    ).takeIf { it != -1 }?.also {
                        val textBigSize = it.toFloat() / context.resources!!.displayMetrics.density
                        txtvBig.textSize = textBigSize
                        txtvSmall.textSize = (textBigSize / 1.5).toFloat()

                        val params = txtvSmall.layoutParams as LayoutParams
                        params.setMargins(
                            params.marginStart,
                            params.topMargin,
                            params.marginEnd,
                            (textBigSize / 6).toInt()
                        )
                        txtvSmall.layoutParams = params
                    }
                    getDimensionPixelSize(
                        amobi.module.common.R.styleable.CommTextBigSmall_textSmallSize,
                        -1
                    ).takeIf { it != -1 }?.also {
                        val textSmallSize =
                            it.toFloat() / context.resources!!.displayMetrics.density
                        txtvSmall.textSize = textSmallSize
                    }

                    // handle text big offset
                    this
                        .getDimensionPixelSize(
                            amobi.module.common.R.styleable.CommTextBigSmall_textBigMarginBottom,
                            -1,
                        ).takeIf { it != -1 }
                        ?.also {
                            val textBigMarginBottom =
                                it.toFloat() / context.resources!!.displayMetrics.density
                            val params = txtvBig.layoutParams as LayoutParams
                            params.setMargins(
                                params.marginStart,
                                params.topMargin,
                                params.marginEnd,
                                textBigMarginBottom.toInt()
                            )
                            txtvBig.layoutParams = params
                        }

                    txtvBig.isAllCaps = getBoolean(
                        amobi.module.common.R.styleable.CommTextBigSmall_textBigAllCaps,
                        false
                    )
                    txtvSmall.isAllCaps = getBoolean(
                        amobi.module.common.R.styleable.CommTextBigSmall_textSmallAllCaps,
                        false
                    )

                    this
                        .getColor(
                            amobi.module.common.R.styleable.CommTextBigSmall_textBigColor,
                            -1,
                        ).takeIf { it != -1 }
                        ?.also { txtvBig.setTextColor(it) }
                    this
                        .getColor(
                            amobi.module.common.R.styleable.CommTextBigSmall_textSmallColor,
                            -1,
                        ).takeIf { it != -1 }
                        ?.also { txtvSmall.setTextColor(it) }
                }
            }
    }

    val txtvBig: TextView get() = binding.txtvBig
    val txtvSmall: TextView get() = binding.txtvSmall
}
