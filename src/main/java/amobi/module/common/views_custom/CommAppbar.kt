package amobi.module.common.views_custom

import amobi.module.common.R
import amobi.module.common.databinding.CommAppbarBinding
import android.content.Context
import android.util.AttributeSet
import android.util.TypedValue
import android.view.LayoutInflater
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.res.ResourcesCompat
import androidx.core.content.withStyledAttributes

class CommAppbar : LinearLayout {
    constructor(context: Context) : super(context)

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        initView(context, attrs)
    }

    constructor(context: Context, attrs: AttributeSet?, defStyle: Int) : super(context, attrs, defStyle) {
        initView(context, attrs)
    }

    private lateinit var binding: CommAppbarBinding

    private fun initView(
        context: Context,
        attributeSet: AttributeSet?,
    ) {
        binding =
            CommAppbarBinding
                .inflate(LayoutInflater.from(context), this, true)
                .apply {
                    context.withStyledAttributes(attributeSet, R.styleable.CommAppbar) {
                        getResourceId(R.styleable.CommAppbar_iconStart, -1).takeIf { it != -1 }
                            ?.also {
                                appbarButtonStart.visibility = VISIBLE
                                appbarButtonStart.setImageResource(it)
//                appbarTitle.layoutParams = with (appbarTitle.layoutParams as LayoutParams) {
//                    setMargins(dimen(R.dimen.margin32dp), topMargin, marginEnd, bottomMargin)
//                    this
//                }
                            }
                        getResourceId(R.styleable.CommAppbar_iconNearEnd, -1).takeIf { it != -1 }
                            ?.also {
                                viewButtonNearEndPlaceHolder.visibility = VISIBLE
                                appbarButtonNearEnd.visibility = VISIBLE
                                appbarButtonNearEnd.setImageResource(it)
                            }
                        getResourceId(R.styleable.CommAppbar_iconEnd, -1).takeIf { it != -1 }
                            ?.also {
                                imgvButtonEnd.visibility = VISIBLE
                                imgvButtonEnd.setImageResource(it)
                            }
                        appbarTitle.text = getString(R.styleable.CommAppbar_textTitle)
                        getResourceId(R.styleable.CommAppbar_textFont, -1).takeIf { it != -1 }
                            ?.also {
                                appbarTitle.typeface = ResourcesCompat.getFont(context, it)
                            }
                        getDimension(R.styleable.CommAppbar_textSize, -1f).takeIf { it != -1f }
                            ?.also {
                                appbarTitle.textSize = TypedValue.applyDimension(
                                    TypedValue.COMPLEX_UNIT_PX,
                                    it,
                                    resources.displayMetrics
                                )
                            }
                        getResourceId(R.styleable.CommAppbar_textColor, -1).takeIf { it != -1 }
                            ?.also {
                                appbarTitle.setTextColor(it)
                            }
                        appbarTitle.isAllCaps =
                            getBoolean(R.styleable.CommAppbar_textAllCaps, false)

                        if (!getBoolean(R.styleable.CommAppbar_underline, true)) root.background =
                            null
                        getColor(R.styleable.CommAppbar_textColor, -1).takeIf { it != -1 }
                            ?.also { appbarTitle.setTextColor(it) }
                    }
                }
    }

    val appbarLayoutTitle: LinearLayout get() = binding.appbarLayoutTitle
    val appbarTextTitle: TextView get() = binding.appbarTitle
    val appbarButtonStart: ImageView get() = binding.appbarButtonStart
    val appbarButtonNearEnd: ImageView get() = binding.appbarButtonNearEnd
    val appbarButtonEnd: ImageView get() = binding.imgvButtonEnd
}
