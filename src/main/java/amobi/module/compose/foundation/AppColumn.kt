package amobi.module.compose.foundation

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier

@Composable
fun AppColumn(
    modifier: Modifier = Modifier,
    contentAlignment: Alignment = Alignment.TopStart,
    content: @Composable ColumnScope.() -> Unit,
) {
    Column(
        modifier = modifier,
        verticalArrangement =
            when (contentAlignment) {
                Alignment.Top, Alignment.TopStart, Alignment.TopEnd, Alignment.TopCenter -> Arrangement.Top
                Alignment.Bottom, Alignment.BottomStart, Alignment.BottomEnd, Alignment.BottomCenter -> Arrangement.Bottom
                Alignment.CenterVertically, Alignment.CenterStart, Alignment.CenterEnd, Alignment.Center -> Arrangement.Center
                else -> Arrangement.Top
            },
        horizontalAlignment =
            when (contentAlignment) {
                Alignment.Start, Alignment.CenterStart, Alignment.TopStart, Alignment.BottomStart -> Alignment.Start
                Alignment.End, Alignment.CenterEnd, Alignment.TopEnd, Alignment.BottomEnd -> Alignment.End
                Alignment.TopCenter, Alignment.BottomCenter, Alignment.Center -> Alignment.CenterHorizontally
                else -> Alignment.Start
            },
        content = content,
    )
}

@Composable
fun AppColumn(
    modifier: Modifier = Modifier,
    verticalArrangement: Arrangement.Vertical = Arrangement.Top,
    horizontalAlignment: Alignment.Horizontal = Alignment.Start,
    content: @Composable ColumnScope.() -> Unit,
) {
    Column(
        modifier = modifier,
        verticalArrangement = verticalArrangement,
        horizontalAlignment = horizontalAlignment,
        content = content,
    )
}

@Composable
fun AppColumnCentered(
    modifier: Modifier = Modifier,
    content: @Composable ColumnScope.() -> Unit,
) {
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally,
        content = content,
    )
}
