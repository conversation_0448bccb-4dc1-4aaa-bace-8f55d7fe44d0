package amobi.module.compose.foundation

import amobi.module.compose.extentions.boxShadow
import amobi.module.compose.extentions.conditional
import amobi.module.compose.theme.AppColors
import amobi.module.compose.theme.AppFontSize
import amobi.module.compose.theme.AppSize
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonColors
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.ButtonElevation
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.DpOffset
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

@Composable
fun AppButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    shape: Shape = RoundedCornerShape(AppSize.CLIP_MEDIUM),
    colors: ButtonColors =
        ButtonDefaults.buttonColors(
            containerColor = AppColors.current.buttonActive,
            disabledContainerColor = AppColors.current.buttonInactive,
            contentColor = AppColors.current.buttonText,
            disabledContentColor = AppColors.current.buttonInactiveText,
        ),
    colorShadow: Color? = AppColors.current.buttonShadow,
    elevation: ButtonElevation? = ButtonDefaults.buttonElevation(),
    border: BorderStroke? = null,
    contentPadding: PaddingValues = PaddingValues(horizontal = 24.dp),
    interactionSource: MutableInteractionSource = remember { MutableInteractionSource() },
    content: @Composable RowScope.() -> Unit,
) {
    Button(
        onClick = onClick,
        modifier =
            modifier
                .conditional(colorShadow != null) {
                    boxShadow(
                        color = colorShadow!!,
                        blurRadius = 6.dp,
                        inset = true,
                        offset = DpOffset(0.dp, (-4).dp),
                        shape = shape,
                    )
                },
        enabled = enabled,
        shape = shape,
        colors = colors,
        elevation = elevation,
        border = border,
        contentPadding = contentPadding,
        interactionSource = interactionSource,
        content = content,
    )
}

@Composable
fun AppButtonText(
    text: String,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    shape: Shape = RoundedCornerShape(AppSize.CLIP_MEDIUM),
    colors: ButtonColors =
        ButtonDefaults.buttonColors(
            containerColor = AppColors.current.buttonActive,
            disabledContainerColor = AppColors.current.buttonInactive,
            contentColor = AppColors.current.buttonText,
            disabledContentColor = AppColors.current.buttonInactiveText,
        ),
    colorShadow: Color? = AppColors.current.buttonShadow,
    textColor: Color = AppColors.current.buttonText,
    elevation: ButtonElevation? = ButtonDefaults.buttonElevation(),
    border: BorderStroke? = null,
    contentPadding: PaddingValues =
        PaddingValues(
            start = 24.dp,
            top = 0.dp,
            end = 24.dp,
            bottom = 0.dp,
        ),
    interactionSource: MutableInteractionSource = remember { MutableInteractionSource() },
    fontSize: TextUnit = AppFontSize.BODY1,
    onClick: () -> Unit,
) {
    AppButton(
        onClick = onClick,
        modifier = modifier,
        enabled = enabled,
        shape = shape,
        colors = colors,
        colorShadow = colorShadow,
        elevation = elevation,
        border = border,
        contentPadding = contentPadding,
        interactionSource = interactionSource,
    ) {
        AppText(
            text = text,
            color = textColor,
            fontSize = fontSize,
            fontWeight = FontWeight.W700,
            letterSpacing = 1.2.sp,
            textAlign = TextAlign.Center,
        )
    }
}
