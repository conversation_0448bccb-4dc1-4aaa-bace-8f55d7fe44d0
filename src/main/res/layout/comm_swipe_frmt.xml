<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <amobi.module.common.views.CommSwipeViewPager
        android:id="@+id/pager"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clickable="false"
        android:overScrollMode="never">

        <LinearLayout
            android:id="@+id/transparentMain"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="horizontal" />

        <LinearLayout
            android:id="@+id/contentMain"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="horizontal" />
    </amobi.module.common.views.CommSwipeViewPager>
</androidx.constraintlayout.widget.ConstraintLayout>