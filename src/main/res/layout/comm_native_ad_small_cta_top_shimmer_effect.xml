<?xml version="1.0" encoding="utf-8"?>
<com.facebook.shimmer.ShimmerFrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/shimmer_view_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:shimmer_duration="3000"
    tools:background="@color/clr_dummy_bg">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerInParent="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">


        <LinearLayout
            android:id="@+id/media_view_prop"
            android:layout_width="match_parent"
            android:layout_height="210dp"
            android:layout_marginHorizontal="4dp"
            android:layout_marginTop="4dp"
            android:background="@drawable/comm_shape_shimmer_effect"
            android:orientation="horizontal"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@id/middle_prop"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/cta_prop" />


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/middle_prop"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:layout_marginVertical="4dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/media_view_prop">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:orientation="horizontal"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <View
                    android:id="@+id/icon_prop"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginStart="4dp"
                    android:layout_weight="0"
                    android:background="@drawable/comm_shape_shimmer_effect"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintDimensionRatio="H,1:1"
                    app:layout_constraintEnd_toStartOf="@id/content_prop"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/content_prop"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_marginStart="10dp"
                    android:orientation="vertical"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/icon_prop"
                    app:layout_constraintTop_toTopOf="parent">

                    <LinearLayout
                        android:id="@+id/headline_prop"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="start"
                        android:orientation="horizontal"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <TextView
                            android:id="@+id/primary_prop"
                            android:layout_width="200dp"
                            android:layout_height="match_parent"
                            android:layout_marginStart="0dp"
                            android:background="@drawable/comm_shape_shimmer_effect"
                            android:lines="1"
                            android:textColor="?attr/themeTextColor"
                            android:textSize="@dimen/txt_size_body1"
                            android:textStyle="bold"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:text="title" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/row_two_prop"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        app:layout_constraintBottom_toTopOf="@+id/body_prop"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/headline_prop">

                        <RatingBar
                            android:id="@+id/rating_bar_prop"
                            style="?android:attr/ratingBarStyleSmall"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="0dp"


                            android:lines="1"
                            android:numStars="5"
                            android:stepSize="0.1"
                            android:textColor="@color/clr_grey"
                            android:textSize="@dimen/gnt_text_size_small"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toEndOf="@id/native_ad_tag_view"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:id="@+id/secondary_prop"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"


                            android:gravity="top"
                            android:lines="1"
                            android:textColor="?attr/themeHintColor"
                            android:textSize="@dimen/gnt_text_size_small"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toEndOf="@id/native_ad_tag_view"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:text="secondary" />

                    </LinearLayout>

                    <TextView
                        android:id="@+id/body_prop"
                        android:layout_width="150dp"
                        android:layout_height="wrap_content"
                        android:background="@drawable/comm_shape_shimmer_effect"
                        android:ellipsize="end"
                        android:gravity="center_vertical"
                        android:maxLines="1"
                        android:textSize="@dimen/gnt_text_size_small"
                        android:visibility="visible"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        tools:text="body" />

                </androidx.constraintlayout.widget.ConstraintLayout>


            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>


        <View
            android:id="@+id/cta_prop"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginHorizontal="4dp"
            android:layout_marginVertical="4dp"
            android:gravity="center"
            android:visibility="invisible"
            app:layout_constraintBottom_toTopOf="@id/media_view_prop"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="@string/dummy_text" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</com.facebook.shimmer.ShimmerFrameLayout>