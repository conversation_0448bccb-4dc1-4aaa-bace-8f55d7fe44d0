<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <com.google.android.gms.ads.nativead.NativeAdView
        android:id="@+id/native_ad_view"
        android:layout_width="match_parent"
        android:layout_height="@dimen/native_ad_large_height"
        android:layout_marginHorizontal="8dp">

        <ViewStub
            android:id="@+id/stubShimmerEffect"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:inflatedId="@+id/nativeAdShimmerEffect"
            android:layout="@layout/comm_native_ad_large_cta_bottom_shimmer_effect"
            android:visibility="gone" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/native_ad_background"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_centerInParent="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible">

            <com.google.android.gms.ads.nativead.MediaView
                android:id="@+id/native_ad_media_view"
                android:layout_width="match_parent"
                android:layout_height="210dp"
                android:layout_marginTop="4dp"
                app:layout_constraintBottom_toTopOf="@id/middle"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/middle"
                android:layout_width="match_parent"
                android:layout_height="65dp"
                android:layout_marginTop="4dp"
                app:layout_constraintBottom_toTopOf="@+id/native_ad_text_cta"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/native_ad_media_view">


                <ImageView
                    android:id="@+id/native_ad_text_icon"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="0"
                    android:scaleType="fitCenter"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintDimensionRatio="H,1:1"
                    app:layout_constraintEnd_toStartOf="@id/content"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:src="@tools:sample/avatars" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/content"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_marginStart="10dp"
                    android:orientation="vertical"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/native_ad_text_icon"
                    app:layout_constraintTop_toTopOf="parent">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/headline"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="start"
                        android:orientation="horizontal"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <TextView
                            android:id="@+id/native_ad_text_primary"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:ellipsize="end"
                            android:maxLines="1"
                            android:textColor="?attr/themeTextColor"
                            android:textSize="@dimen/txt_size_body1"
                            android:textStyle="bold"
                            app:autoSizeMaxTextSize="@dimen/txt_size_body1"
                            app:autoSizeMinTextSize="@dimen/txt_size_hint"
                            app:autoSizeStepGranularity="1sp"
                            app:autoSizeTextType="uniform"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toStartOf="@+id/native_ad_tag_view"
                            app:layout_constraintHorizontal_bias="0.0"
                            app:layout_constraintHorizontal_chainStyle="packed"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintWidth_default="wrap"
                            tools:text="title" />

                        <TextView
                            android:id="@+id/native_ad_tag_view"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="top|bottom"
                            android:layout_marginStart="4dp"
                            android:layout_marginTop="1dp"
                            android:background="@drawable/comm_native_ad_tag"
                            android:paddingHorizontal="2dp"
                            android:paddingVertical="1dp"
                            android:text=" AD "
                            android:textColor="?attr/themeTextSurroundByPrimaryColor"
                            android:textSize="@dimen/txt_size_smallest"
                            android:textStyle="bold"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toEndOf="@+id/native_ad_text_primary"
                            app:layout_constraintTop_toTopOf="parent" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <LinearLayout
                        android:id="@+id/row_two"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        app:layout_constraintBottom_toTopOf="@+id/native_ad_text_body"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/headline">

                        <RatingBar
                            android:id="@+id/native_ad_rating_bar"
                            style="?android:attr/ratingBarStyleSmall"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="0dp"
                            android:lines="1"
                            android:numStars="5"
                            android:stepSize="0.1"
                            android:textColor="@color/clr_grey"
                            android:textSize="@dimen/gnt_text_size_small"
                            android:theme="@style/NativeAdRatingBarStyle"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toEndOf="@id/native_ad_tag_view"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:id="@+id/native_ad_text_secondary"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:gravity="top"
                            android:lines="1"
                            android:textColor="?attr/themeHintColor"
                            android:textSize="@dimen/gnt_text_size_small"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toEndOf="@id/native_ad_tag_view"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:text="secondary" />

                    </LinearLayout>

                    <TextView
                        android:id="@+id/native_ad_text_body"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:gravity="center_vertical"
                        android:maxLines="1"
                        android:textColor="?attr/themeHintColor"
                        android:textSize="@dimen/gnt_text_size_small"
                        android:visibility="visible"
                        app:layout_constraintBottom_toBottomOf="parent"
                        tools:text="body" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>


            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/native_ad_text_cta"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginVertical="4dp"
                android:background="@drawable/comm_ripple_rect_bg_blue"
                android:fontFamily="@font/roboto_medium"
                android:gravity="center"
                android:lines="1"
                android:textColor="?attr/themeTextSurroundByPrimaryColor"
                android:textSize="@dimen/txt_size_title"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/middle"
                tools:text="@string/dummy_text" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </com.google.android.gms.ads.nativead.NativeAdView>

</merge>
