<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="@dimen/dimen_dialog_margin"
    tools:background="@color/clr_dummy_bg"
    tools:context="amobi.module.rate.me.RateMeDialog">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="@dimen/dimen_radius_corner"
        app:cardElevation="0dp">

        <ImageView
            android:id="@+id/imgvBackground"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:adjustViewBounds="true"
            android:visibility="gone" />

        <LinearLayout
            android:id="@+id/llytRateMeDialog"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:background="@color/clr_white"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="visible">


            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp">

                <ImageView
                    android:id="@+id/imageViewRateLogo"
                    android:layout_width="75dp"
                    android:layout_height="75dp"
                    android:layout_marginTop="20dp"
                    android:adjustViewBounds="true"
                    android:src="@drawable/svg_emoji_smile"
                    android:scaleType="fitCenter"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <LinearLayout
                    android:id="@+id/llyt_close"
                    android:layout_width="@dimen/app_bar_height"
                    android:layout_height="@dimen/app_bar_height"
                    android:layout_margin="4dp"
                    android:background="@drawable/comm_ripple_circle_bg_primary"
                    android:contentDescription="@android:string/cancel"
                    android:gravity="center"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <ImageView
                        android:id="@+id/imgvClose"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/svg_comm_ic_close"
                        app:tint="@color/clr_black" />
                </LinearLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="12dp"
                android:layout_marginTop="16dp"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/txtvTitle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="4dp"
                    android:fontFamily="@font/roboto_medium"
                    android:gravity="center"
                    android:lineSpacingExtra="2sp"
                    android:text="@string/rate_me_oh_no"
                    android:textColor="@color/clr_black"
                    android:textSize="@dimen/txt_size_title" />

                <TextView
                    android:id="@+id/txtvContent1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/roboto_regular"
                    android:gravity="center"
                    android:lineSpacingExtra="2sp"
                    android:text="@string/new_txtid_rate_app_content_new_1"
                    android:textColor="@color/clr_black"
                    android:textSize="@dimen/txt_size_body2" />

                <TextView
                    android:id="@+id/txtvContent2"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:fontFamily="@font/roboto_regular"
                    android:gravity="center"
                    android:text="@string/new_txtid_rate_app_content_new_2"
                    android:textColor="@color/clr_black"
                    android:textSize="@dimen/txt_size_body2" />
            </LinearLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/clytViewFiveStars"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginVertical="16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/imageViewRateLogo">

                <TextView
                    android:id="@+id/messageTextView_4"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="4dp"
                    android:layout_marginBottom="4dp"
                    android:fontFamily="@font/roboto_medium"
                    android:text="@string/rate_me_the_best_we_can_get"
                    android:textColor="?attr/colorPrimary"
                    android:textSize="@dimen/txt_size_smallest"
                    android:visibility="gone"
                    app:layout_constraintBottom_toTopOf="@+id/imageViewArrow"
                    app:layout_constraintEnd_toEndOf="@+id/imageViewArrow" />

                <ImageView
                    android:id="@+id/imageViewArrow"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="28dp"
                    android:layout_marginBottom="8dp"
                    android:adjustViewBounds="true"
                    android:src="@drawable/svg_rate_arrow"
                    android:visibility="gone"
                    app:layout_constraintBottom_toTopOf="@+id/ratingBar"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:tint="?attr/colorPrimary" />

                <LinearLayout
                    android:id="@+id/ratingBar"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:orientation="horizontal"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent">

                    <ImageView
                        android:id="@+id/imageViewStar1"
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:layout_marginHorizontal="4dp"
                        android:background="@drawable/comm_ripple_circle_bg_primary"
                        android:src="@drawable/svg_rating_empty" />

                    <ImageView
                        android:id="@+id/imageViewStar2"
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:layout_marginHorizontal="4dp"
                        android:background="@drawable/comm_ripple_circle_bg_primary"
                        android:src="@drawable/svg_rating_empty" />

                    <ImageView
                        android:id="@+id/imageViewStar3"
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:layout_marginHorizontal="4dp"
                        android:background="@drawable/comm_ripple_circle_bg_primary"
                        android:src="@drawable/svg_rating_empty" />

                    <ImageView
                        android:id="@+id/imageViewStar4"
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:layout_marginHorizontal="4dp"
                        android:background="@drawable/comm_ripple_circle_bg_primary"
                        android:src="@drawable/svg_rating_empty" />

                    <ImageView
                        android:id="@+id/imageViewStar5"
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:layout_marginHorizontal="4dp"
                        android:background="@drawable/comm_ripple_circle_bg_primary"
                        android:src="@drawable/svg_rating_empty" />
                </LinearLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <TextView
                android:id="@+id/bttn_ok"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="28dp"
                android:layout_marginBottom="32dp"
                android:background="@drawable/comm_ripple_rect_bg_primary"
                android:clipToPadding="false"
                android:elevation="8dp"
                android:fontFamily="@font/roboto_medium"
                android:gravity="center"
                android:minWidth="@dimen/bttn_min_width"
                android:minHeight="@dimen/bttn_min_height"
                android:outlineProvider="bounds"
                android:paddingHorizontal="2dp"
                android:paddingVertical="6dp"
                android:text="@string/rate_me_rate_us"
                android:textAllCaps="true"
                android:textColor="?attr/themeTextSurroundByPrimaryColor"
                android:textSize="@dimen/txt_size_body1"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                tools:visibility="visible" />
        </LinearLayout>

    </androidx.cardview.widget.CardView>
</LinearLayout>


