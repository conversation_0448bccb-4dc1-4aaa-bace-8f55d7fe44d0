<resources xmlns:tools="http://schemas.android.com/tools" tools:context=".view_presenter.BaseActivity">

    <style name="Comm.Light" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <item name="colorPrimary">@color/clr_red</item>
        <item name="colorPrimaryVariant">@color/clr_transparent</item>
        <item name="colorAccent">@color/clr_dark_grey</item>
        <item name="colorOnPrimary">?colorPrimary</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/clr_teal_200</item>
        <item name="colorSecondaryVariant">@color/clr_teal_700</item>
        <item name="colorOnSecondary">@color/clr_black</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <item name="backgroundColor">@color/clr_white</item>
        <!-- Customize your theme here. -->

        <item name="themeTextColor">@color/clr_black</item>
        <item name="themeTextSurroundByPrimaryColor">@color/clr_white</item>
        <item name="themeHintColor">#727676</item>
        <item name="themeTextShadowColor">?attr/themeHintColor</item>
        <item name="themeGreyColor">#727676</item>
        <item name="themeTextInputHintColor">#808080</item>
        <item name="themeSubtleColor">#727676</item>
        <item name="themeTabItemColor">#707579</item>
        <item name="themeBtnColor">@color/clr_black</item>
        <item name="themeBtnDeleteColor">@color/clr_purple_200</item>
        <item name="themeSecondaryColor">@color/clr_white</item>
        <item name="themeSelectedColor">#F1F1F1</item>
        <item name="themeNavBarColor">?attr/themeSecondaryColor</item>
        <item name="themeNavBarItemColor">?attr/themeTextColor</item>
        <item name="themeNavBarDoneColor">?attr/colorPrimary</item>
        <item name="themeRippleColor">?colorPrimary</item>
        <item name="android:textViewStyle">@style/RobotoTextViewStyle</item>
        <item name="android:buttonStyle">@style/RobotoButtonStyle</item>
    </style>

    <style name="Comm.Dark" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <item name="android:windowTranslucentNavigation">true</item>
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/clr_red</item>
        <item name="colorPrimaryDark">@color/clr_dark_grey</item>
        <item name="colorAccent">@color/clr_dark_grey</item>
        <item name="colorPrimaryVariant">@color/clr_transparent</item>
        <item name="colorOnPrimary">?colorPrimary</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/clr_teal_200</item>
        <item name="colorSecondaryVariant">@color/clr_teal_700</item>
        <item name="colorOnSecondary">@color/clr_black</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <item name="backgroundColor">@color/clr_dark_grey</item>
        <!-- Customize your theme here. -->

        <item name="themeTextColor">@color/clr_white</item>
        <item name="themeTextSurroundByPrimaryColor">@color/clr_white</item>
        <item name="themeHintColor">#858B91</item>
        <item name="themeTextShadowColor">?attr/themeHintColor</item>
        <item name="themeGreyColor">#858B91</item>
        <item name="themeTextInputHintColor">#808080</item>
        <item name="themeSubtleColor">#858B91</item>
        <item name="themeTabItemColor">#B6B6B6</item>
        <item name="themeBtnColor">@color/clr_white</item>
        <item name="themeBtnDeleteColor">@color/clr_red</item>
        <item name="themeSecondaryColor">#2A2B30</item>
        <item name="themeSelectedColor">#2A2B30</item>
        <item name="themeNavBarColor">@color/clr_dark_grey</item>
        <item name="themeNavBarItemColor">?attr/themeTextColor</item>
        <item name="themeNavBarDoneColor">?attr/colorPrimary</item>
        <item name="themeRippleColor">?colorPrimary</item>
        <item name="android:textViewStyle">@style/RobotoTextViewStyle</item>
        <item name="android:buttonStyle">@style/RobotoButtonStyle</item>
    </style>


    <style name="Comm.AdTheme" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <item name="android:statusBarColor">@color/clr_transparent</item>
        <item name="android:background">@color/clr_black</item>
        <item name="android:windowBackground">@color/clr_black</item>
        <item name="android:windowExitAnimation">@null</item>
        <item name="android:windowEnterAnimation">@null</item>
    </style>
</resources>