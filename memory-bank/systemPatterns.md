# EzMath AI - System Patterns

## Architecture Overview
Based on the package structure and dependencies, EzMath AI appears to follow a Clean Architecture approach with MVVM presentation pattern:

```
com.amobilab.ezmath.ai
├── app        # Application level components
├── presentation # UI components, ViewModels, Compose screens
├── data       # Data sources, repositories, models
├── di         # Dependency injection modules
├── utils      # Utility functions
└── admob      # Ad integration
```

## Design Patterns

### Clean Architecture
The app is structured in layers, likely following:
- Presentation layer (UI components, ViewModels)
- Domain layer (Use cases, business logic)
- Data layer (Repositories, data sources)

### MVVM (Model-View-ViewModel)
- **View**: Jetpack Compose UI components
- **ViewModel**: Maintains UI state, handles UI logic
- **Model**: Data layer with repositories

### Dependency Injection
- Uses Dagger Hilt for dependency injection
- Components are modular and testable

## UI Component System
- Custom foundation components with "App" prefix (AppColumn, AppBox, etc.)
- Consistent design system throughout the application
- Follows Material 3 design principles via Jetpack Compose

## State Management
- Likely uses Compose state management patterns
- ViewModels with StateFlow/LiveData for UI state
- Room database for persistent storage

## Navigation
- Jetpack Compose Navigation for screen transitions
- Hierarchical navigation structure

## Modular Structure
The app uses several modules:
- `:amobi_common`: Shared utilities
- `:amobi_compose`: Compose components
- `:amobi_compose_theme`: UI theme
- `:amobi_otp`: One-time password functionality
- `:amobi_rate_me`: App rating system
- `:amobi_openai`: OpenAI integration

## Data Flow
```
UI (Compose) ⟷ ViewModel ⟷ Repository ⟷ [Local DB | Remote API]
```

## AI Integration
- Firebase VertexAI for AI functionality
- Likely uses a repository pattern to abstract AI service details

## Authentication
- Firebase Auth for user authentication
- Google Sign-In integration

## Persistence
- Room Database for local storage
- Firebase Firestore for cloud storage

## Build Variants
Three product flavors:
- Alpha: Development/testing version
- Dev: Development build
- Product: Production release 