package amobi.module.openai.api.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * Model permission details.
 */
@Serializable
public data class ModelPermission(
    @SerialName("id") public val id: String,
    @SerialName("created") public val created: Long,
    @SerialName("allow_create_engine") public val allowCreateEngine: <PERSON><PERSON><PERSON>,
    @SerialName("allow_sampling") public val allowSampling: <PERSON><PERSON><PERSON>,
    @SerialName("allow_logprobs") public val allowLogprobs: <PERSON><PERSON><PERSON>,
    @SerialName("allow_search_indices") public val allowSearchIndices: <PERSON><PERSON><PERSON>,
    @SerialName("allow_view") public val allowView: <PERSON><PERSON><PERSON>,
    @SerialName("allow_fine_tuning") public val allowFineTuning: <PERSON><PERSON>an,
    @SerialName("organization") public val organization: String,
    @SerialName("is_blocking") public val isBlocking: <PERSON><PERSON>an,
)
