package amobi.module.openai.client.internal.api

import amobi.module.openai.api.chat.ChatCompletion
import amobi.module.openai.api.chat.ChatCompletionChunk
import amobi.module.openai.api.chat.ChatCompletionRequest
import amobi.module.openai.api.core.RequestOptions
import amobi.module.openai.client.Chat
import amobi.module.openai.client.internal.extension.requestOptions
import amobi.module.openai.client.internal.extension.streamEventsFrom
import amobi.module.openai.client.internal.extension.streamRequestOf
import amobi.module.openai.client.internal.http.HttpRequester
import amobi.module.openai.client.internal.http.perform
import io.ktor.client.call.*
import io.ktor.client.request.*
import io.ktor.http.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow

internal class ChatApi(private val requester: HttpRequester) : Chat {
    override suspend fun chatCompletion(
        request: ChatCompletionRequest,
        requestOptions: RequestOptions?
    ): ChatCompletion {
        return requester.perform {
            it.post {
                url(path = ApiPath.ChatCompletions)
                setBody(request)
                contentType(ContentType.Application.Json)
                requestOptions(requestOptions)
            }.body()
        }
    }

    override fun chatCompletions(
        request: ChatCompletionRequest,
        requestOptions: RequestOptions?
    ): Flow<ChatCompletionChunk> {
        val builder = HttpRequestBuilder().apply {
            method = HttpMethod.Post
            url(path = ApiPath.ChatCompletions)
            setBody(streamRequestOf(request))
            contentType(ContentType.Application.Json)
            accept(ContentType.Text.EventStream)
            headers {
                append(HttpHeaders.CacheControl, "no-cache")
                append(HttpHeaders.Connection, "keep-alive")
            }
            requestOptions(requestOptions)
        }
        return flow {
            requester.perform(builder) { response -> streamEventsFrom(response) }
        }
    }
}
