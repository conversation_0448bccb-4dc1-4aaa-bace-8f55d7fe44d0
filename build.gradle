// python build.py product
// python3 build.py product

// Build app with uninstalled all
// From the Run menu -> Edit Configurations... -> Before Launch -> Add Gradle-aware Make -> ":app:uninstallAll"

// Check library dependencies
// ./gradlew app:dependencies > dependencies.txt

// git submodule update --init --remote --merge
// git submodule foreach git checkout master

// chmod +x check_elf_alignment.sh
// ./check_elf_alignment.sh qr.apk

plugins {
    alias(libs.plugins.android.library)
    alias(libs.plugins.jetbrains.kotlin.android)
}

android {
    namespace 'amobi.module.common'

    defaultConfig {
        compileSdk libs.versions.compileSdkVersion.get().toInteger()
        minSdk libs.versions.minSdkVersion.get().toInteger()
        targetSdk libs.versions.targetSdkVersion.get().toInteger()
    }

    buildFeatures {
        viewBinding true
        dataBinding true
    }
    compileOptions {
        sourceCompatibility libs.versions.javaVersion.get().toInteger()
        targetCompatibility libs.versions.javaVersion.get().toInteger()
    }
}

dependencies {
    implementation libs.androidx.core.ktx

    implementation libs.user.messaging.platform

    implementation libs.app.update
    implementation libs.app.update.ktx

    implementation libs.androidx.constraintlayout
    implementation libs.androidx.appcompat
    implementation libs.androidx.material
    implementation libs.play.services.ads
    implementation libs.okhttp

    implementation libs.lifecycle.extensions
    implementation libs.lifecycle.common.java8

    implementation platform(libs.firebase.bom)
    implementation libs.firebase.config
    implementation libs.firebase.crashlytics
    implementation libs.firebase.analytics

    implementation libs.shimmer
    implementation libs.joda.time


    implementation libs.androidx.window
    implementation libs.androidx.window.core.android
}